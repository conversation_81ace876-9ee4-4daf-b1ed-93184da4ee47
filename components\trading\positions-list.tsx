
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { Activity, RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';
import { TradeSide } from '@prisma/client';
import { toast } from 'sonner';

interface Position {
  id: string;
  symbol: string;
  side: TradeSide;
  quantity: number;
  entryPrice: number;
  currentPrice?: number;
  unrealizedPnl?: number;
  isActive: boolean;
  createdAt: string;
}

export function PositionsList() {
  const { user } = useAuth();
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchPositions = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/trading/positions?userId=${user.id}&mode=${user.mode}&tradingType=${user.tradingType || 'SPOT'}`);
      if (!response.ok) throw new Error('Erreur récupération positions');
      
      const data = await response.json();
      setPositions(data || []);
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement des positions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPositions();
    
    // Auto-refresh toutes les 30 secondes
    const intervalId: NodeJS.Timeout = setInterval(() => {
      fetchPositions();
    }, 30000);
    return () => clearInterval(intervalId);
  }, [user, user?.tradingType]); // Recharger aussi quand le tradingType change

  const formatPrice = (price?: number | null) => {
    // Vérification et conversion robuste en nombre
    const numPrice = typeof price === 'number' && !isNaN(price) ? price : null;
    if (!numPrice || numPrice === 0) return '--';
    
    if (numPrice > 1) return numPrice.toFixed(2);
    if (numPrice > 0.1) return numPrice.toFixed(4);
    return numPrice.toFixed(6);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch {
      return '--';
    }
  };

  const getSideIcon = (side: TradeSide) => {
    return side === TradeSide.LONG ? (
      <TrendingUp className="w-4 h-4 text-green-400" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-400" />
    );
  };

  const calculatePnl = (position: Position) => {
    const currentPrice = parseFloat(String(position.currentPrice || 0));
    const entryPrice = parseFloat(String(position.entryPrice || 0));
    const quantity = parseFloat(String(position.quantity || 0));
    
    if (!currentPrice || !entryPrice || !quantity || isNaN(currentPrice) || isNaN(entryPrice) || isNaN(quantity)) {
      return null;
    }
    
    const priceDiff = position.side === TradeSide.LONG 
      ? currentPrice - entryPrice
      : entryPrice - currentPrice;
    
    return priceDiff * quantity;
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-white flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Positions ouvertes
          </CardTitle>
          <CardDescription className="text-slate-400">
            {user?.mode === 'DEMO' ? '🟢 Positions démo' : '🔴 Positions réelles'} - {positions?.filter?.(p => p?.isActive)?.length || 0} active(s)
          </CardDescription>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={fetchPositions}
          disabled={loading}
          className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
        >
          <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {positions?.filter?.(p => p?.isActive)?.length > 0 ? (
            positions
              .filter(p => p?.isActive)
              .map((position, index) => {
                const pnl = calculatePnl(position) || position.unrealizedPnl;
                
                return (
                  <div 
                    key={position?.id || `position-${index}`}
                    className="p-3 bg-slate-800/30 rounded-lg border border-slate-700/50"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getSideIcon(position?.side)}
                        <span className={`font-medium ${
                          position?.side === TradeSide.LONG ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {position?.side === TradeSide.LONG ? 'LONG' : 'SHORT'}
                        </span>
                        <span className="text-white font-medium">{position?.symbol}</span>
                      </div>
                      <Badge 
                        className={`${
                          pnl && pnl > 0 
                            ? 'bg-green-600/20 text-green-400' 
                            : pnl && pnl < 0 
                            ? 'bg-red-600/20 text-red-400'
                            : 'bg-gray-600/20 text-gray-400'
                        }`}
                      >
                        {pnl ? `${pnl > 0 ? '+' : ''}$${formatPrice(pnl)}` : 'N/A'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div>
                        <div className="text-slate-400">Taille</div>
                        <div className="text-white font-medium">{position?.quantity || 0}</div>
                      </div>
                      <div>
                        <div className="text-slate-400">Prix d'entrée</div>
                        <div className="text-white font-medium">
                          ${formatPrice(position?.entryPrice)}
                        </div>
                      </div>
                      <div>
                        <div className="text-slate-400">Prix actuel</div>
                        <div className="text-white font-medium">
                          ${formatPrice(position?.currentPrice)}
                        </div>
                      </div>
                      <div>
                        <div className="text-slate-400">Ouvert le</div>
                        <div className="text-white">{formatDate(position?.createdAt || '')}</div>
                      </div>
                    </div>

                    <div className="mt-2 pt-2 border-t border-slate-700">
                      <div className="flex items-center justify-between">
                        <span className="text-slate-400">Valeur totale:</span>
                        <span className="text-white font-medium">
                          ${(() => {
                            const qty = parseFloat(String(position?.quantity || 0));
                            const price = parseFloat(String(position?.currentPrice || position?.entryPrice || 0));
                            const total = !isNaN(qty) && !isNaN(price) ? qty * price : 0;
                            return formatPrice(total);
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })
          ) : (
            <div className="text-center text-slate-400 py-8">
              {loading ? 'Chargement...' : 'Aucune position ouverte'}
              {!loading && user?.mode === 'DEMO' && (
                <div className="mt-2 text-sm">
                  Vos positions apparaîtront ici après vos premiers trades
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
