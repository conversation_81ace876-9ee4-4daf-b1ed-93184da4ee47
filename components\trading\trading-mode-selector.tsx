

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { 
  TrendingUp, 
  BarChart3, 
  Calendar, 
  Users, 
  Bot,
  Zap,
  DollarSign,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';

const TRADING_TYPES = [
  {
    id: 'SPOT',
    name: 'Trading Spot',
    description: 'Achat et vente au comptant',
    icon: DollarSign,
    features: ['Trading instantané', 'Pas de levier', 'Propriété directe'],
    risk: 'Faible',
    color: 'bg-blue-600/20 text-blue-400 border-blue-600/30'
  },
  {
    id: 'PERPETUAL_FUTURES',
    name: 'Futures Perpétuels',
    description: 'Contrats à terme sans échéance',
    icon: TrendingUp,
    features: ['<PERSON><PERSON> jusqu\'à 125x', 'Positions long/short', 'Financement perpétuel'],
    risk: 'Élevé',
    color: 'bg-orange-600/20 text-orange-400 border-orange-600/30'
  },
  {
    id: 'STANDARD_FUTURES',
    name: 'Futures Standards',
    description: 'Contrats à terme avec échéance',
    icon: Calendar,
    features: ['Date d\'expiration', 'Règlement physique', 'Levier contrôlé'],
    risk: 'Modéré',
    color: 'bg-purple-600/20 text-purple-400 border-purple-600/30'
  },
  {
    id: 'COPY_TRADING',
    name: 'Copy Trading',
    description: 'Copie automatique de traders experts',
    icon: Users,
    features: ['Suivre des experts', 'Gestion automatique', 'Diversification'],
    risk: 'Variable',
    color: 'bg-green-600/20 text-green-400 border-green-600/30'
  },
  {
    id: 'BOT_TRADING',
    name: 'Trading Bot',
    description: 'Bots automatisés et stratégies',
    icon: Bot,
    features: ['Trading 24/7', 'Grilles automatiques', 'DCA et arbitrage'],
    risk: 'Modéré',
    color: 'bg-cyan-600/20 text-cyan-400 border-cyan-600/30'
  }
];

export function TradingModeSelector() {
  const { user, updateTradingType } = useAuth();
  const [selectedType, setSelectedType] = useState(user?.tradingType || 'SPOT');
  const [loading, setLoading] = useState(false);

  // Synchroniser l'état local avec l'utilisateur du store
  useEffect(() => {
    if (user?.tradingType) {
      setSelectedType(user.tradingType);
    }
  }, [user?.tradingType]);

  const handleTypeChange = async (tradingType: string) => {
    if (!user || loading) return;

    setLoading(true);
    try {
      console.log('Changement de mode:', { userId: user.id, tradingType });
      
      const response = await fetch('/api/auth/update-trading-type', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user.id, 
          tradingType 
        }),
      });

      const result = await response.json();
      console.log('Réponse API:', result);

      if (!response.ok) {
        throw new Error(result.error || 'Erreur lors de la mise à jour');
      }

      // Mettre à jour d'abord l'état local
      setSelectedType(tradingType);
      
      // Puis mettre à jour l'état utilisateur dans le store Zustand
      updateTradingType(tradingType);
      
      toast.success(`Mode ${TRADING_TYPES.find(t => t.id === tradingType)?.name} activé`);

    } catch (error: any) {
      console.error('Erreur changement mode:', error);
      toast.error(`Erreur: ${error.message || 'Changement de mode échoué'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Mode de Trading
        </CardTitle>
        <CardDescription className="text-slate-400">
          Sélectionnez votre type de trading préféré
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {TRADING_TYPES.map((type) => {
            const Icon = type.icon;
            const isSelected = selectedType === type.id;
            
            return (
              <div
                key={type.id}
                className={`
                  relative p-4 rounded-lg border-2 cursor-pointer transition-all
                  ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                  ${isSelected 
                    ? 'bg-slate-800/80 border-blue-500 ring-2 ring-blue-500/30' 
                    : 'bg-slate-800/30 border-slate-700 hover:bg-slate-800/50 hover:border-slate-600'
                  }
                `}
                onClick={() => !loading && handleTypeChange(type.id)}
              >
                {isSelected && (
                  <div className="absolute -top-2 -right-2">
                    <Badge className="bg-blue-600 text-white">
                      <Zap className="w-3 h-3 mr-1" />
                      Actif
                    </Badge>
                  </div>
                )}
                
                <div className="flex items-center mb-3">
                  <div className={`p-2 rounded-lg ${type.color} mr-3`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold text-sm">{type.name}</h3>
                    <p className="text-slate-400 text-xs">{type.description}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  {type.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-xs">
                      <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mr-2"></div>
                      <span className="text-slate-300">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-3 flex items-center justify-between">
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${
                      type.risk === 'Faible' 
                        ? 'border-green-600 text-green-400' 
                        : type.risk === 'Modéré'
                        ? 'border-yellow-600 text-yellow-400'
                        : type.risk === 'Élevé'
                        ? 'border-red-600 text-red-400'
                        : 'border-gray-600 text-gray-400'
                    }`}
                  >
                    Risque: {type.risk}
                  </Badge>
                </div>
              </div>
            );
          })}
        </div>

        {selectedType && (
          <div className="mt-6 p-4 bg-slate-800/50 rounded-lg border border-slate-700">
            <h4 className="text-white font-medium mb-2">
              {loading ? 'Changement en cours...' : `Mode actuel: ${TRADING_TYPES.find(t => t.id === selectedType)?.name}`}
            </h4>
            <p className="text-slate-400 text-sm">
              {loading ? 'Veuillez patienter...' : (
                user?.mode === 'DEMO' 
                  ? '🟢 Mode démo - Utilisez ce mode pour vous entraîner sans risque'
                  : '🔴 Mode réel - Attention: Vous tradez avec de vrais fonds'
              )}
            </p>
          </div>
        )}

        <div className="mt-4 p-3 bg-yellow-600/10 border border-yellow-600/30 rounded-lg">
          <div className="flex items-start">
            <div className="text-yellow-400 mr-2 mt-0.5">⚠️</div>
            <div className="text-sm">
              <p className="text-yellow-200 font-medium">Avertissement Important</p>
              <p className="text-yellow-300/80 mt-1">
                Certains modes de trading impliquent des risques élevés. 
                Assurez-vous de comprendre les risques avant de trader.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
