
import { NextRequest, NextResponse } from 'next/server';
import { DemoTradingService } from '@/lib/demo-trading';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const mode = searchParams.get('mode') as 'DEMO' | 'LIVE';
    const tradingType = searchParams.get('tradingType') || 'SPOT';

    if (!userId || !mode) {
      return NextResponse.json(
        { error: 'UserId et mode requis' },
        { status: 400 }
      );
    }

    if (mode === 'DEMO') {
      const positions = await DemoTradingService.getDemoPositions(userId);
      return NextResponse.json(positions);
    } else {
      // Mode live - récupérer les positions BingX selon le type de trading
      const positions = await bingxApi.getPositionsByType(tradingType);
      return NextResponse.json(positions);
    }

  } catch (error) {
    console.error('Erreur récupération positions:', error);
    return NextResponse.json(
      { error: 'Erreur récupération des positions' },
      { status: 500 }
    );
  }
}
