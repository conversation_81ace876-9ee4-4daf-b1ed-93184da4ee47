

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { TradingMode, TradingType } from '@prisma/client';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, mode, tradingType } = body;

    if (!email || !mode) {
      return NextResponse.json(
        { error: 'Email et mode requis' },
        { status: 400 }
      );
    }

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Utilisateur déjà existant' },
        { status: 409 }
      );
    }

    // Créer un nouvel utilisateur
    const user = await prisma.user.create({
      data: {
        email,
        name: email.split('@')[0],
        mode: mode as TradingMode,
        tradingType: (tradingType as TradingType) || TradingType.SPOT,
        demoBalance: {
          create: {
            balance: 1000,
            currency: 'USDT'
          }
        }
      },
      include: { demoBalance: true }
    });

    return NextResponse.json({
      id: user.id,
      email: user.email,
      name: user.name,
      mode: user.mode,
      tradingType: user.tradingType
    });

  } catch (error) {
    console.error('Erreur signup:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
