
import { NextRequest, NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');

    const ticker = symbol 
      ? await bingxApi.getTicker24hr(symbol)
      : await bingxApi.getTicker24hr();

    return NextResponse.json(ticker);

  } catch (error) {
    console.error('Erreur récupération ticker:', error);
    return NextResponse.json(
      { error: 'Erreur récupération des prix' },
      { status: 500 }
    );
  }
}
