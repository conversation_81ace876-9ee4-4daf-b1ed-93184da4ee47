

import { NextRequest, NextResponse } from 'next/server';
import { randomBytes } from 'crypto';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    // Générer un token CSRF simple
    const csrfToken = randomBytes(32).toString('hex');
    
    return NextResponse.json({
      csrfToken
    });

  } catch (error) {
    console.error('Erreur CSRF:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
