

import { NextRequest, NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol') || 'BTC-USDT';
    const interval = searchParams.get('interval') || '1h';
    const limit = parseInt(searchParams.get('limit') || '100');

    console.log('Récupération klines perpétuels:', { symbol, interval, limit });

    const response = await bingxApi.getPerpetualKlines(symbol, interval, limit);
    
    // Formater les données pour le graphique
    const klines = response?.data || [];
    const formattedData = klines.map((kline: any[]) => ({
      time: kline[0],
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5])
    }));

    console.log(`Klines perpétuels formatées: ${formattedData.length}`);

    return NextResponse.json(formattedData);

  } catch (error) {
    console.error('Erreur récupération klines perpétuels:', error);
    
    // Fallback vers l'API spot si les perpétuels échouent
    try {
      const { searchParams } = new URL(request.url);
      const symbol = searchParams.get('symbol') || 'BTC-USDT';
      const interval = searchParams.get('interval') || '1h';
      const limit = parseInt(searchParams.get('limit') || '100');

      const response = await bingxApi.getKlines(symbol, interval, limit);
      const klines = response?.data || [];
      const formattedData = klines.map((kline: any[]) => ({
        time: kline[0],
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5])
      }));

      return NextResponse.json(formattedData);
    } catch (fallbackError) {
      console.error('Erreur fallback klines:', fallbackError);
      return NextResponse.json(
        { error: 'Erreur récupération des données de graphique' },
        { status: 500 }
      );
    }
  }
}
