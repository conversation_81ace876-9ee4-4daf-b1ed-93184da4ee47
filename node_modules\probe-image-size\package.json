{"name": "probe-image-size", "version": "7.2.3", "description": "Get image size without full download (JPG, GIF, PNG, WebP, BMP, TIFF, PSD)", "keywords": ["image", "size", "jpg", "jpeg", "ico", "gif", "png", "webp", "tiff", "bmp", "svg", "psd"], "repository": "nodeca/probe-image-size", "license": "MIT", "files": ["index.js", "http.js", "stream.js", "sync.js", "lib/"], "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html", "report-coveralls": "nyc --reporter=lcov mocha"}, "mocha": {"timeout": 5000}, "dependencies": {"lodash.merge": "^4.6.2", "needle": "^2.5.2", "stream-parser": "~0.3.1"}, "devDependencies": {"eslint": "^8.2.0", "mocha": "^9.1.3", "nyc": "^15.1.0"}}