
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { TradingMode } from '@prisma/client';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, mode } = body;

    if (!userId || !mode) {
      return NextResponse.json(
        { error: 'UserId et mode requis' },
        { status: 400 }
      );
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: { mode: mode as TradingMode }
    });

    return NextResponse.json({
      id: user.id,
      email: user.email,
      name: user.name,
      mode: user.mode
    });

  } catch (error) {
    console.error('Erreur switch mode:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
