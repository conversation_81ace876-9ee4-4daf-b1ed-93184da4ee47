

import { NextRequest, NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tradingType = searchParams.get('tradingType') || 'SPOT';

    console.log('Récupération positions pour type:', tradingType);

    let positions = [];

    try {
      positions = await bingxApi.getPositionsByType(tradingType);
    } catch (apiError) {
      console.error('Erreur API positions:', apiError);
      // Retourner un tableau vide si l'API spécifique échoue
      positions = [];
    }

    // Formater les positions selon le type de trading
    const formattedPositions = positions.map((position: any) => {
      if (tradingType === 'PERPETUAL_FUTURES') {
        return {
          id: position.positionId || `${position.symbol}-${position.positionSide}`,
          symbol: position.symbol,
          side: position.positionSide === 'LONG' ? 'LONG' : 'SHORT',
          quantity: parseFloat(position.positionAmt || '0'),
          entryPrice: parseFloat(position.entryPrice || '0'),
          markPrice: parseFloat(position.markPrice || '0'),
          unrealizedPnl: parseFloat(position.unrealizedProfit || '0'),
          percentage: parseFloat(position.percentage || '0'),
          leverage: position.leverage || '1',
          isActive: parseFloat(position.positionAmt || '0') !== 0,
          createdAt: new Date().toISOString() // BingX ne fournit pas toujours cette info
        };
      } else {
        // Format pour les autres types ou données génériques
        return {
          id: position.id || `${position.symbol}-${Date.now()}`,
          symbol: position.symbol || '',
          side: position.side || 'BUY',
          quantity: parseFloat(position.quantity || position.positionAmt || '0'),
          entryPrice: parseFloat(position.entryPrice || position.avgPrice || '0'),
          currentPrice: parseFloat(position.currentPrice || position.markPrice || '0'),
          unrealizedPnl: parseFloat(position.unrealizedPnl || position.unrealizedProfit || '0'),
          isActive: true,
          createdAt: position.createdAt || new Date().toISOString()
        };
      }
    });

    console.log(`Positions ${tradingType} formatées:`, formattedPositions.length);

    return NextResponse.json(formattedPositions);

  } catch (error) {
    console.error('Erreur récupération positions:', error);
    return NextResponse.json(
      { error: 'Erreur récupération des positions' },
      { status: 500 }
    );
  }
}
