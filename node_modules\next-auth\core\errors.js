"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;
exports.adapterErrorHandler = adapterErrorHandler;
exports.capitalize = capitalize;
exports.eventsErrorHandler = eventsErrorHandler;
exports.upperSnake = upperSnake;
var _regenerator = _interopRequireDefault(require("@babel/runtime/regenerator"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _wrapNativeSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/wrapNativeSuper"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var UnknownError = exports.UnknownError = function (_Error) {
  function UnknownError(error) {
    var _message;
    var _this;
    (0, _classCallCheck2.default)(this, UnknownError);
    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);
    _this.name = "UnknownError";
    _this.code = error.code;
    if (error instanceof Error) {
      _this.stack = error.stack;
    }
    return _this;
  }
  (0, _inherits2.default)(UnknownError, _Error);
  return (0, _createClass2.default)(UnknownError, [{
    key: "toJSON",
    value: function toJSON() {
      return {
        name: this.name,
        message: this.message,
        stack: this.stack
      };
    }
  }]);
}((0, _wrapNativeSuper2.default)(Error));
var OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {
  function OAuthCallbackError() {
    var _this2;
    (0, _classCallCheck2.default)(this, OAuthCallbackError);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));
    (0, _defineProperty2.default)(_this2, "name", "OAuthCallbackError");
    return _this2;
  }
  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);
  return (0, _createClass2.default)(OAuthCallbackError);
}(UnknownError);
var AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {
  function AccountNotLinkedError() {
    var _this3;
    (0, _classCallCheck2.default)(this, AccountNotLinkedError);
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));
    (0, _defineProperty2.default)(_this3, "name", "AccountNotLinkedError");
    return _this3;
  }
  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);
  return (0, _createClass2.default)(AccountNotLinkedError);
}(UnknownError);
var MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {
  function MissingAPIRoute() {
    var _this4;
    (0, _classCallCheck2.default)(this, MissingAPIRoute);
    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
      args[_key3] = arguments[_key3];
    }
    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));
    (0, _defineProperty2.default)(_this4, "name", "MissingAPIRouteError");
    (0, _defineProperty2.default)(_this4, "code", "MISSING_NEXTAUTH_API_ROUTE_ERROR");
    return _this4;
  }
  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);
  return (0, _createClass2.default)(MissingAPIRoute);
}(UnknownError);
var MissingSecret = exports.MissingSecret = function (_UnknownError4) {
  function MissingSecret() {
    var _this5;
    (0, _classCallCheck2.default)(this, MissingSecret);
    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
      args[_key4] = arguments[_key4];
    }
    _this5 = _callSuper(this, MissingSecret, [].concat(args));
    (0, _defineProperty2.default)(_this5, "name", "MissingSecretError");
    (0, _defineProperty2.default)(_this5, "code", "NO_SECRET");
    return _this5;
  }
  (0, _inherits2.default)(MissingSecret, _UnknownError4);
  return (0, _createClass2.default)(MissingSecret);
}(UnknownError);
var MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {
  function MissingAuthorize() {
    var _this6;
    (0, _classCallCheck2.default)(this, MissingAuthorize);
    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
      args[_key5] = arguments[_key5];
    }
    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));
    (0, _defineProperty2.default)(_this6, "name", "MissingAuthorizeError");
    (0, _defineProperty2.default)(_this6, "code", "CALLBACK_CREDENTIALS_HANDLER_ERROR");
    return _this6;
  }
  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);
  return (0, _createClass2.default)(MissingAuthorize);
}(UnknownError);
var MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {
  function MissingAdapter() {
    var _this7;
    (0, _classCallCheck2.default)(this, MissingAdapter);
    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {
      args[_key6] = arguments[_key6];
    }
    _this7 = _callSuper(this, MissingAdapter, [].concat(args));
    (0, _defineProperty2.default)(_this7, "name", "MissingAdapterError");
    (0, _defineProperty2.default)(_this7, "code", "EMAIL_REQUIRES_ADAPTER_ERROR");
    return _this7;
  }
  (0, _inherits2.default)(MissingAdapter, _UnknownError6);
  return (0, _createClass2.default)(MissingAdapter);
}(UnknownError);
var MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {
  function MissingAdapterMethods() {
    var _this8;
    (0, _classCallCheck2.default)(this, MissingAdapterMethods);
    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {
      args[_key7] = arguments[_key7];
    }
    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));
    (0, _defineProperty2.default)(_this8, "name", "MissingAdapterMethodsError");
    (0, _defineProperty2.default)(_this8, "code", "MISSING_ADAPTER_METHODS_ERROR");
    return _this8;
  }
  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);
  return (0, _createClass2.default)(MissingAdapterMethods);
}(UnknownError);
var UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {
  function UnsupportedStrategy() {
    var _this9;
    (0, _classCallCheck2.default)(this, UnsupportedStrategy);
    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {
      args[_key8] = arguments[_key8];
    }
    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));
    (0, _defineProperty2.default)(_this9, "name", "UnsupportedStrategyError");
    (0, _defineProperty2.default)(_this9, "code", "CALLBACK_CREDENTIALS_JWT_ERROR");
    return _this9;
  }
  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);
  return (0, _createClass2.default)(UnsupportedStrategy);
}(UnknownError);
var InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {
  function InvalidCallbackUrl() {
    var _this10;
    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);
    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {
      args[_key9] = arguments[_key9];
    }
    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));
    (0, _defineProperty2.default)(_this10, "name", "InvalidCallbackUrl");
    (0, _defineProperty2.default)(_this10, "code", "INVALID_CALLBACK_URL_ERROR");
    return _this10;
  }
  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);
  return (0, _createClass2.default)(InvalidCallbackUrl);
}(UnknownError);
function upperSnake(s) {
  return s.replace(/([A-Z])/g, "_$1").toUpperCase();
}
function capitalize(s) {
  return "".concat(s[0].toUpperCase()).concat(s.slice(1));
}
function eventsErrorHandler(methods, logger) {
  return Object.keys(methods).reduce(function (acc, name) {
    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {
      var method,
        _args = arguments;
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            method = methods[name];
            _context.next = 4;
            return method.apply(void 0, _args);
          case 4:
            return _context.abrupt("return", _context.sent);
          case 7:
            _context.prev = 7;
            _context.t0 = _context["catch"](0);
            logger.error("".concat(upperSnake(name), "_EVENT_ERROR"), _context.t0);
          case 10:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 7]]);
    }));
    return acc;
  }, {});
}
function adapterErrorHandler(adapter, logger) {
  if (!adapter) return;
  return Object.keys(adapter).reduce(function (acc, name) {
    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {
      var _len10,
        args,
        _key10,
        method,
        e,
        _args2 = arguments;
      return _regenerator.default.wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {
              args[_key10] = _args2[_key10];
            }
            logger.debug("adapter_".concat(name), {
              args: args
            });
            method = adapter[name];
            _context2.next = 6;
            return method.apply(void 0, args);
          case 6:
            return _context2.abrupt("return", _context2.sent);
          case 9:
            _context2.prev = 9;
            _context2.t0 = _context2["catch"](0);
            logger.error("adapter_error_".concat(name), _context2.t0);
            e = new UnknownError(_context2.t0);
            e.name = "".concat(capitalize(name), "Error");
            throw e;
          case 15:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 9]]);
    }));
    return acc;
  }, {});
}