generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/plateforme_trading_bingx/app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model User {
  id              String      @id @default(cuid())
  email           String      @unique
  name            String?
  mode            TradingMode @default(DEMO) // Mode de trading actuel
  tradingType     TradingType @default(SPOT) // Type de trading (Spot, Futures, etc.)
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  
  // Relations
  demoBalance     DemoBalance?
  tradesHistory   Trade[]
  
  @@map("users")
}

model DemoBalance {
  id        String   @id @default(cuid())
  userId    String   @unique
  balance   Decimal  @default(1000) // Solde demo en USDT
  currency  String   @default("USDT")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("demo_balances")
}

model Trade {
  id          String      @id @default(cuid())
  userId      String
  symbol      String      // Ex: BTC-USDT
  side        TradeSide   // BUY ou SELL
  type        TradeType   // MARKET ou LIMIT
  quantity    Decimal     // Quantité
  price       Decimal?    // Prix (null pour market orders)
  executedPrice Decimal?  // Prix d'exécution réel
  status      TradeStatus // PENDING, FILLED, CANCELLED, FAILED
  mode        TradingMode // DEMO ou LIVE
  profit      Decimal?    // Profit/Loss calculé
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("trades")
}

model Position {
  id          String      @id @default(cuid())
  userId      String
  symbol      String      // Ex: BTC-USDT
  side        TradeSide   // LONG ou SHORT
  quantity    Decimal     // Quantité en position
  entryPrice  Decimal     // Prix moyen d'entrée
  currentPrice Decimal?   // Prix actuel
  unrealizedPnl Decimal?  // P&L non réalisé
  mode        TradingMode // DEMO ou LIVE
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  @@map("positions")
}

// Enums
enum TradingMode {
  DEMO
  LIVE
}

enum TradingType {
  SPOT
  PERPETUAL_FUTURES
  STANDARD_FUTURES
  COPY_TRADING
  BOT_TRADING
}

enum TradeSide {
  BUY
  SELL
  LONG
  SHORT
}

enum TradeType {
  MARKET
  LIMIT
}

enum TradeStatus {
  PENDING
  FILLED
  CANCELLED
  FAILED
}
