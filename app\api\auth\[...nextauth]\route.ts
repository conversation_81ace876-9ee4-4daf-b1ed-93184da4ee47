

import NextAuth from 'next-auth'
import { NextAuthOptions } from 'next-auth'
import { prisma } from '@/lib/db'
import CredentialsProvider from 'next-auth/providers/credentials'
import { TradingMode } from '@prisma/client'

export const dynamic = "force-dynamic";

const authOptions: NextAuthOptions = {
  // Pas d'adapter avec CredentialsProvider
  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: '<PERSON><PERSON>',
      credentials: {
        email: { label: 'Email', type: 'email' },
        mode: { label: 'Mode', type: 'text' }
      },
      async authorize(credentials) {
        console.log('NextAuth authorize called with:', credentials);
        
        if (!credentials?.email || !credentials?.mode) {
          console.error('Credentials manquantes:', credentials);
          return null
        }

        try {
          console.log('Recherche utilisateur:', credentials.email);
          
          // Trouver ou créer l'utilisateur
          let user = await prisma.user.findUnique({
            where: { email: credentials.email },
            include: { demoBalance: true }
          })

          console.log('Utilisateur trouvé:', user ? 'Oui' : 'Non');

          if (!user) {
            console.log('Création nouvel utilisateur');
            user = await prisma.user.create({
              data: {
                email: credentials.email,
                name: credentials.email.split('@')[0],
                mode: credentials.mode as TradingMode,
                demoBalance: {
                  create: {
                    balance: 1000,
                    currency: 'USDT'
                  }
                }
              },
              include: { demoBalance: true }
            })
            console.log('Utilisateur créé:', user.id);
          } else {
            // Mettre à jour le mode si nécessaire
            if (user.mode !== credentials.mode) {
              console.log('Mise à jour mode:', credentials.mode);
              user = await prisma.user.update({
                where: { id: user.id },
                data: { mode: credentials.mode as TradingMode },
                include: { demoBalance: true }
              })
            }
          }

          const result = {
            id: user.id,
            email: user.email,
            name: user.name,
            mode: user.mode
          };
          
          console.log('Autorisation réussie:', result);
          return result;
          
        } catch (error) {
          console.error('Erreur dans authorize:', error)
          return null
        }
      }
    })
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      console.log('JWT callback - token:', token, 'user:', user);
      if (user) {
        token.mode = (user as any).mode
        token.name = user.name
        token.email = user.email
      }
      return token
    },
    session: async ({ session, token }) => {
      console.log('Session callback - session:', session, 'token:', token);
      if (token && session.user) {
        (session.user as any).id = token.sub
        ;(session.user as any).mode = token.mode
        session.user.name = token.name
        session.user.email = token.email
      }
      return session
    }
  },
  pages: {
    signIn: '/',
    error: '/'
  },
  session: {
    strategy: 'jwt'
  }
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }
