
import { NextRequest, NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';
import { DemoTradingService } from '@/lib/demo-trading';
import { TradeSide, TradeType, TradingMode } from '@prisma/client';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, symbol, side, type, quantity, price, mode } = body;

    if (!userId || !symbol || !side || !type || !quantity || !mode) {
      return NextResponse.json(
        { error: 'Paramètres manquants' },
        { status: 400 }
      );
    }

    if (mode === 'DEMO') {
      // Mode démo
      const trade = await DemoTradingService.executeDemoOrder(
        userId,
        symbol,
        side as TradeSide,
        type as TradeType,
        parseFloat(quantity),
        price ? parseFloat(price) : undefined
      );

      return NextResponse.json({
        success: true,
        trade,
        message: 'Ordre démo exécuté avec succès'
      });
    } else {
      // Mode live - utiliser l'API BingX réelle
      const order = await bingxApi.createOrder(
        symbol,
        side as 'BUY' | 'SELL',
        type as 'MARKET' | 'LIMIT',
        quantity,
        price
      );

      return NextResponse.json({
        success: true,
        order,
        message: 'Ordre réel placé avec succès'
      });
    }

  } catch (error) {
    console.error('Erreur création ordre:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erreur serveur' },
      { status: 500 }
    );
  }
}
