
import { NextRequest, NextResponse } from 'next/server';
import { DemoTradingService } from '@/lib/demo-trading';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'UserId requis' },
        { status: 400 }
      );
    }

    const result = await DemoTradingService.resetDemoBalance(userId);

    return NextResponse.json({
      success: true,
      message: result.message
    });

  } catch (error) {
    console.error('Erreur reset demo:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
