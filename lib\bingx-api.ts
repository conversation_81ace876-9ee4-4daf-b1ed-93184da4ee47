
import crypto from 'crypto';

const BINGX_BASE_URL = 'https://open-api.bingx.com';

interface BingXApiConfig {
  apiKey: string;
  secretKey: string;
  baseURL?: string;
}

class BingXAPI {
  private apiKey: string;
  private secretKey: string;
  private baseURL: string;

  constructor(config: BingXApiConfig) {
    this.apiKey = config.apiKey;
    this.secretKey = config.secretKey;
    this.baseURL = config.baseURL || BINGX_BASE_URL;
  }

  // Helper method to get the correct API path based on trading type
  private getApiPath(tradingType: string): string {
    switch (tradingType) {
      case 'SPOT':
        return '/openApi/spot/v1';
      case 'PERPETUAL_FUTURES':
        return '/openApi/swap/v2';
      case 'STANDARD_FUTURES':
        return '/openApi/future/v1';
      case 'COPY_TRADING':
        return '/openApi/copy/v1';
      case 'BOT_TRADING':
        return '/openApi/grid/v1';
      default:
        return '/openApi/spot/v1';
    }
  }

  private generateSignature(queryString: string): string {
    return crypto
      .createHmac('sha256', this.secretKey)
      .update(queryString)
      .digest('hex');
  }

  private async makeRequest(
    endpoint: string,
    method: 'GET' | 'POST' | 'DELETE' = 'GET',
    params: Record<string, any> = {},
    requiresAuth: boolean = false
  ): Promise<any> {
    const timestamp = Date.now();
    
    // BingX exige toujours le timestamp, même pour les endpoints publics
    params.timestamp = timestamp;

    const queryString = new URLSearchParams(params).toString();
    let url = `${this.baseURL}${endpoint}`;

    if (requiresAuth) {
      const signature = this.generateSignature(queryString);
      url += `?${queryString}&signature=${signature}`;
    } else if (queryString) {
      url += `?${queryString}`;
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (requiresAuth) {
      headers['X-BX-APIKEY'] = this.apiKey;
    }

    try {
      const response = await fetch(url, {
        method,
        headers,
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('BingX API Error:', error);
      throw error;
    }
  }

  // Méthodes publiques (ne nécessitent pas d'authentification)
  async getAllSymbols() {
    return this.makeRequest('/openApi/spot/v1/common/symbols');
  }

  async getTicker24hr(symbol?: string) {
    const params = symbol ? { symbol } : {};
    return this.makeRequest('/openApi/spot/v1/ticker/24hr', 'GET', params);
  }

  async getKlines(symbol: string, interval: string = '1m', limit: number = 100) {
    return this.makeRequest('/openApi/spot/v1/market/kline', 'GET', {
      symbol,
      interval,
      limit
    });
  }

  async getOrderBook(symbol: string, limit: number = 100) {
    return this.makeRequest('/openApi/spot/v1/depth', 'GET', {
      symbol,
      limit
    });
  }

  // Méthodes privées (nécessitent l'authentification)
  async getAccountInfo() {
    return this.makeRequest('/openApi/spot/v1/account/balance', 'GET', {}, true);
  }

  async getBalance() {
    const accountInfo = await this.getAccountInfo();
    return accountInfo?.data?.balances || [];
  }

  async createOrder(
    symbol: string,
    side: 'BUY' | 'SELL',
    type: 'MARKET' | 'LIMIT',
    quantity: string,
    price?: string
  ) {
    const params: any = {
      symbol,
      side,
      type,
      quantity,
    };

    if (type === 'LIMIT' && price) {
      params.price = price;
      params.timeInForce = 'GTC';
    }

    return this.makeRequest('/openApi/spot/v1/order', 'POST', params, true);
  }

  async getOrder(symbol: string, orderId: number) {
    return this.makeRequest('/openApi/spot/v1/order', 'GET', {
      symbol,
      orderId
    }, true);
  }

  async getOpenOrders(symbol?: string) {
    const params = symbol ? { symbol } : {};
    return this.makeRequest('/openApi/spot/v1/openOrders', 'GET', params, true);
  }

  async getAllOrders(symbol: string, limit: number = 100) {
    return this.makeRequest('/openApi/spot/v1/allOrders', 'GET', {
      symbol,
      limit
    }, true);
  }

  async cancelOrder(symbol: string, orderId: number) {
    return this.makeRequest('/openApi/spot/v1/order', 'DELETE', {
      symbol,
      orderId
    }, true);
  }

  // ========== PERPETUAL FUTURES METHODS ==========
  
  async getPerpetualBalance() {
    return this.makeRequest('/openApi/swap/v2/user/balance', 'GET', {}, true);
  }

  async getPerpetualPositions() {
    return this.makeRequest('/openApi/swap/v2/user/positions', 'GET', {}, true);
  }

  async createPerpetualOrder(
    symbol: string,
    side: 'BUY' | 'SELL',
    type: 'MARKET' | 'LIMIT',
    quantity: string,
    price?: string,
    positionSide?: 'LONG' | 'SHORT'
  ) {
    const params: any = {
      symbol,
      side,
      type,
      quantity,
      positionSide: positionSide || 'BOTH'
    };

    if (type === 'LIMIT' && price) {
      params.price = price;
      params.timeInForce = 'GTC';
    }

    return this.makeRequest('/openApi/swap/v2/trade/order', 'POST', params, true);
  }

  async getPerpetualTickers() {
    return this.makeRequest('/openApi/swap/v2/quote/ticker', 'GET', {});
  }

  async getPerpetualKlines(symbol: string, interval: string = '1m', limit: number = 100) {
    return this.makeRequest('/openApi/swap/v2/quote/klines', 'GET', {
      symbol,
      interval,
      limit
    });
  }

  // ========== STANDARD FUTURES METHODS ==========
  
  async getStandardFuturesBalance() {
    return this.makeRequest('/openApi/contract/v1/balance', 'GET', {}, true);
  }

  async getStandardFuturesPositions() {
    return this.makeRequest('/openApi/contract/v1/allPosition', 'GET', {}, true);
  }

  async getStandardFuturesOrders(symbol?: string, startTime?: number, endTime?: number, limit: number = 100) {
    const params: any = { limit };
    if (symbol) params.symbol = symbol;
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    
    return this.makeRequest('/openApi/contract/v1/allOrders', 'GET', params, true);
  }

  // ========== COPY TRADING METHODS ==========
  
  async getCopyTradingPortfolio() {
    return this.makeRequest('/openApi/copy/v1/user/portfolio', 'GET', {}, true);
  }

  async getCopyTradingLeaders() {
    return this.makeRequest('/openApi/copy/v1/leaders', 'GET', {});
  }

  async followLeader(leaderId: string, copyAmount: string) {
    return this.makeRequest('/openApi/copy/v1/follow', 'POST', {
      leaderId,
      copyAmount
    }, true);
  }

  // ========== BOT TRADING METHODS ==========
  
  async getGridBots() {
    return this.makeRequest('/openApi/grid/v1/user/bots', 'GET', {}, true);
  }

  async createGridBot(
    symbol: string,
    gridType: 'ARITHMETIC' | 'GEOMETRIC',
    gridNum: number,
    upperPrice: string,
    lowerPrice: string,
    investment: string
  ) {
    return this.makeRequest('/openApi/grid/v1/bot/create', 'POST', {
      symbol,
      gridType,
      gridNum,
      upperPrice,
      lowerPrice,
      investment
    }, true);
  }

  async stopGridBot(botId: string) {
    return this.makeRequest('/openApi/grid/v1/bot/stop', 'POST', {
      botId
    }, true);
  }

  // ========== UNIFIED METHODS FOR DIFFERENT TRADING TYPES ==========
  
  async getBalanceByType(tradingType: string) {
    try {
      switch (tradingType) {
        case 'SPOT':
          return this.getBalance();
          
        case 'PERPETUAL_FUTURES':
          const perpBalance = await this.getPerpetualBalance();
          // Convertir le format des futures perpétuels vers le format standard
          if (perpBalance?.data?.balance) {
            const balance = perpBalance.data.balance;
            return [{
              asset: balance.asset || 'USDT',
              free: balance.availableMargin || balance.balance || '0',
              locked: balance.usedMargin || '0',
              equity: balance.equity || '0',
              unrealizedProfit: balance.unrealizedProfit || '0'
            }];
          }
          return [];
          
        case 'STANDARD_FUTURES':
          const contractBalance = await this.getStandardFuturesBalance();
          // Convertir le format des contrats standards vers le format standard
          if (contractBalance?.data) {
            return contractBalance.data.map((balance: any) => ({
              asset: balance.asset,
              free: balance.availableBalance || balance.balance || '0',
              locked: (parseFloat(balance.balance || '0') - parseFloat(balance.availableBalance || '0')).toString(),
              crossWalletBalance: balance.crossWalletBalance || '0',
              crossUnPnl: balance.crossUnPnl || '0',
              availableBalance: balance.availableBalance || '0'
            }));
          }
          return [];
          
        case 'COPY_TRADING':
          // Pour l'instant, fallback vers spot
          return this.getBalance();
          
        case 'BOT_TRADING':
          // Pour l'instant, fallback vers spot
          return this.getBalance();
          
        default:
          return this.getBalance();
      }
    } catch (error) {
      console.error(`Erreur API ${tradingType}:`, error);
      // Fallback vers l'API spot en cas d'erreur
      return this.getBalance();
    }
  }

  async getPositionsByType(tradingType: string) {
    try {
      switch (tradingType) {
        case 'PERPETUAL_FUTURES':
          return this.getPerpetualPositions();
          
        case 'STANDARD_FUTURES':
          const contractPositions = await this.getStandardFuturesPositions();
          // Convertir le format des positions de contrats standards
          if (contractPositions?.data) {
            return contractPositions.data.map((position: any) => ({
              id: `${position.symbol}-${position.positionSide}-${position.time}`,
              symbol: position.symbol,
              side: position.positionSide,
              quantity: Math.abs(parseFloat(position.positionAmt || '0')),
              entryPrice: parseFloat(position.entryPrice || '0'),
              markPrice: parseFloat(position.currentPrice || '0'),
              unrealizedPnl: parseFloat(position.unrealizedProfit || '0'),
              leverage: parseFloat(position.leverage || '1'),
              initialMargin: parseFloat(position.initialMargin || '0'),
              isIsolated: position.isolated || false,
              isActive: Math.abs(parseFloat(position.positionAmt || '0')) > 0,
              createdAt: new Date(position.time || Date.now()).toISOString()
            }));
          }
          return [];
          
        default:
          return [];
      }
    } catch (error) {
      console.error(`Erreur positions ${tradingType}:`, error);
      return [];
    }
  }

  async createOrderByType(
    tradingType: string,
    symbol: string,
    side: 'BUY' | 'SELL',
    type: 'MARKET' | 'LIMIT',
    quantity: string,
    price?: string,
    extra?: any
  ) {
    switch (tradingType) {
      case 'SPOT':
        return this.createOrder(symbol, side, type, quantity, price);
      case 'PERPETUAL_FUTURES':
        return this.createPerpetualOrder(symbol, side, type, quantity, price, extra?.positionSide);
      default:
        return this.createOrder(symbol, side, type, quantity, price);
    }
  }
}

export default BingXAPI;

// Instance globale pour l'API BingX
export const bingxApi = new BingXAPI({
  apiKey: process.env.BINGX_API_KEY || '',
  secretKey: process.env.BINGX_SECRET_KEY || '',
});
