{"name": "@floating-ui/react", "version": "0.26.28", "description": "Floating UI for React", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.react.umd.js", "module": "./dist/floating-ui.react.esm.js", "unpkg": "./dist/floating-ui.react.umd.min.js", "types": "./dist/floating-ui.react.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/floating-ui.react.d.mts", "default": "./dist/floating-ui.react.mjs"}, "types": "./dist/floating-ui.react.d.ts", "module": "./dist/floating-ui.react.esm.js", "default": "./dist/floating-ui.react.umd.js"}, "./utils": {"import": {"types": "./dist/floating-ui.react.utils.d.mts", "default": "./dist/floating-ui.react.utils.mjs"}, "types": "./dist/floating-ui.react.utils.d.ts", "module": "./dist/floating-ui.react.utils.esm.js", "default": "./dist/floating-ui.react.utils.umd.js"}}, "sideEffects": false, "files": ["dist", "utils"], "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/react"}, "homepage": "https://floating-ui.com/docs/react", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning", "react", "react-dom"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"tabbable": "^6.0.0", "@floating-ui/react-dom": "^2.1.2", "@floating-ui/utils": "^0.2.8"}, "devDependencies": {"@babel/preset-react": "^7.23.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-icons": "^1.3.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "resize-observer-polyfill": "^1.5.1", "use-isomorphic-layout-effect": "^1.1.2", "config": "0.0.0"}, "scripts": {"lint": "eslint .", "format": "prettier --write .", "clean": "<PERSON><PERSON><PERSON> dist out-tsc utils", "test": "vitest run", "test:watch": "vitest watch", "build": "rollup -c", "build:api": "build-api --tsc tsconfig.lib.json --aec api-extractor.json --aec api-extractor.utils.json", "dev": "vite", "publint": "publint", "typecheck": "tsc -b"}}