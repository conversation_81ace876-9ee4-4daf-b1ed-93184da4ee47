(function(da,ea){"object"===typeof exports&&"undefined"!==typeof module?module.exports=ea():"function"===typeof define&&define.amd?define(ea):da.createREGL=ea()})(this,function(){function da(a,b){this.id=Db++;this.type=a;this.data=b}function ea(a){if(0===a.length)return[];var b=a.charAt(0),c=a.charAt(a.length-1);if(1<a.length&&b===c&&('"'===b||"'"===b))return['"'+a.substr(1,a.length-2).replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'];if(b=/\[(false|true|null|\d+|'[^']*'|"[^"]*")\]/.exec(a))return ea(a.substr(0,
b.index)).concat(ea(b[1])).concat(ea(a.substr(b.index+b[0].length)));b=a.split(".");if(1===b.length)return['"'+a.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'];a=[];for(c=0;c<b.length;++c)a=a.concat(ea(b[c]));return a}function bb(a){return"["+ea(a).join("][")+"]"}function cb(a,b){if("function"===typeof a)return new da(0,a);if("number"===typeof a||"boolean"===typeof a)return new da(5,a);if(Array.isArray(a))return new da(6,a.map(function(a,d){return cb(a,b+"["+d+"]")}));if(a instanceof da)return a}
function Eb(){var a={"":0},b=[""];return{id:function(c){var d=a[c];if(d)return d;d=a[c]=b.length;b.push(c);return d},str:function(a){return b[a]}}}function Fb(a,b,c){function d(){var b=window.innerWidth,d=window.innerHeight;a!==document.body&&(d=f.getBoundingClientRect(),b=d.right-d.left,d=d.bottom-d.top);f.width=c*b;f.height=c*d}var f=document.createElement("canvas");O(f.style,{border:0,margin:0,padding:0,top:0,left:0,width:"100%",height:"100%"});a.appendChild(f);a===document.body&&(f.style.position=
"absolute",O(a.style,{margin:0,padding:0}));var e;a!==document.body&&"function"===typeof ResizeObserver?(e=new ResizeObserver(function(){setTimeout(d)}),e.observe(a)):window.addEventListener("resize",d,!1);d();return{canvas:f,onDestroy:function(){e?e.disconnect():window.removeEventListener("resize",d);a.removeChild(f)}}}function Gb(a,b){function c(c){try{return a.getContext(c,b)}catch(f){return null}}return c("webgl")||c("experimental-webgl")||c("webgl-experimental")}function db(a){return"string"===
typeof a?a.split():a}function eb(a){return"string"===typeof a?document.querySelector(a):a}function Hb(a){var b=a||{},c,d,f,e;a={};var m=[],q=[],u="undefined"===typeof window?1:window.devicePixelRatio,r=!1,k={},n=function(a){},t=function(){};"string"===typeof b?c=document.querySelector(b):"object"===typeof b&&("string"===typeof b.nodeName&&"function"===typeof b.appendChild&&"function"===typeof b.getBoundingClientRect?c=b:"function"===typeof b.drawArrays||"function"===typeof b.drawElements?(e=b,f=e.canvas):
("gl"in b?e=b.gl:"canvas"in b?f=eb(b.canvas):"container"in b&&(d=eb(b.container)),"attributes"in b&&(a=b.attributes),"extensions"in b&&(m=db(b.extensions)),"optionalExtensions"in b&&(q=db(b.optionalExtensions)),"onDone"in b&&(n=b.onDone),"profile"in b&&(r=!!b.profile),"pixelRatio"in b&&(u=+b.pixelRatio),"cachedCode"in b&&(k=b.cachedCode)));c&&("canvas"===c.nodeName.toLowerCase()?f=c:d=c);if(!e){if(!f){c=Fb(d||document.body,n,u);if(!c)return null;f=c.canvas;t=c.onDestroy}void 0===a.premultipliedAlpha&&
(a.premultipliedAlpha=!0);e=Gb(f,a)}return e?{gl:e,canvas:f,container:d,extensions:m,optionalExtensions:q,pixelRatio:u,profile:r,cachedCode:k,onDone:n,onDestroy:t}:(t(),n("webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org"),null)}function Ib(a,b){function c(b){b=b.toLowerCase();var c;try{c=d[b]=a.getExtension(b)}catch(f){}return!!c}for(var d={},f=0;f<b.extensions.length;++f){var e=b.extensions[f];if(!c(e))return b.onDestroy(),b.onDone('"'+e+'" extension is not supported by the current WebGL context, try upgrading your system or a different browser'),
null}b.optionalExtensions.forEach(c);return{extensions:d,restore:function(){Object.keys(d).forEach(function(a){if(d[a]&&!c(a))throw Error("(regl): error restoring extension "+a);})}}}function S(a,b){for(var c=Array(a),d=0;d<a;++d)c[d]=b(d);return c}function fb(a){var b,c;b=(65535<a)<<4;a>>>=b;c=(255<a)<<3;a>>>=c;b|=c;c=(15<a)<<2;a>>>=c;b|=c;c=(3<a)<<1;return b|c|a>>>c>>1}function gb(){function a(a){a:{for(var b=16;268435456>=b;b*=16)if(a<=b){a=b;break a}a=0}b=c[fb(a)>>2];return 0<b.length?b.pop():
new ArrayBuffer(a)}function b(a){c[fb(a.byteLength)>>2].push(a)}var c=S(8,function(){return[]});return{alloc:a,free:b,allocType:function(b,c){var e=null;switch(b){case 5120:e=new Int8Array(a(c),0,c);break;case 5121:e=new Uint8Array(a(c),0,c);break;case 5122:e=new Int16Array(a(2*c),0,c);break;case 5123:e=new Uint16Array(a(2*c),0,c);break;case 5124:e=new Int32Array(a(4*c),0,c);break;case 5125:e=new Uint32Array(a(4*c),0,c);break;case 5126:e=new Float32Array(a(4*c),0,c);break;default:return null}return e.length!==
c?e.subarray(0,c):e},freeType:function(a){b(a.buffer)}}}function fa(a){return!!a&&"object"===typeof a&&Array.isArray(a.shape)&&Array.isArray(a.stride)&&"number"===typeof a.offset&&a.shape.length===a.stride.length&&(Array.isArray(a.data)||P(a.data))}function hb(a,b,c,d,f,e){for(var m=0;m<b;++m)for(var q=a[m],u=0;u<c;++u)for(var r=q[u],k=0;k<d;++k)f[e++]=r[k]}function ib(a,b,c,d,f){for(var e=1,m=c+1;m<b.length;++m)e*=b[m];var q=b[c];if(4===b.length-c){var u=b[c+1],r=b[c+2];b=b[c+3];for(m=0;m<q;++m)hb(a[m],
u,r,b,d,f),f+=e}else for(m=0;m<q;++m)ib(a[m],b,c+1,d,f),f+=e}function Ha(a){return Ia[Object.prototype.toString.call(a)]|0}function jb(a,b){for(var c=0;c<b.length;++c)a[c]=b[c]}function kb(a,b,c,d,f,e,m){for(var q=0,u=0;u<c;++u)for(var r=0;r<d;++r)a[q++]=b[f*u+e*r+m]}function Jb(a,b,c,d){function f(b){this.id=u++;this.buffer=a.createBuffer();this.type=b;this.usage=35044;this.byteLength=0;this.dimension=1;this.dtype=5121;this.persistentData=null;c.profile&&(this.stats={size:0})}function e(b,c,d){b.byteLength=
c.byteLength;a.bufferData(b.type,c,d)}function m(a,b,c,d,h,p){a.usage=c;if(Array.isArray(b)){if(a.dtype=d||5126,0<b.length)if(Array.isArray(b[0])){h=lb(b);for(var y=d=1;y<h.length;++y)d*=h[y];a.dimension=d;b=Ua(b,h,a.dtype);e(a,b,c);p?a.persistentData=b:B.freeType(b)}else"number"===typeof b[0]?(a.dimension=h,h=B.allocType(a.dtype,b.length),jb(h,b),e(a,h,c),p?a.persistentData=h:B.freeType(h)):P(b[0])&&(a.dimension=b[0].length,a.dtype=d||Ha(b[0])||5126,b=Ua(b,[b.length,b[0].length],a.dtype),e(a,b,c),
p?a.persistentData=b:B.freeType(b))}else if(P(b))a.dtype=d||Ha(b),a.dimension=h,e(a,b,c),p&&(a.persistentData=new Uint8Array(new Uint8Array(b.buffer)));else if(fa(b)){h=b.shape;var f=b.stride,y=b.offset,w=0,z=0,k=0,m=0;1===h.length?(w=h[0],z=1,k=f[0],m=0):2===h.length&&(w=h[0],z=h[1],k=f[0],m=f[1]);a.dtype=d||Ha(b.data)||5126;a.dimension=z;h=B.allocType(a.dtype,w*z);kb(h,b.data,w,z,k,m,y);e(a,h,c);p?a.persistentData=h:B.freeType(h)}else b instanceof ArrayBuffer&&(a.dtype=5121,a.dimension=h,e(a,b,
c),p&&(a.persistentData=new Uint8Array(new Uint8Array(b))))}function q(c){b.bufferCount--;d(c);a.deleteBuffer(c.buffer);c.buffer=null;delete r[c.id]}var u=0,r={};f.prototype.bind=function(){a.bindBuffer(this.type,this.buffer)};f.prototype.destroy=function(){q(this)};var k=[];c.profile&&(b.getTotalBufferSize=function(){var a=0;Object.keys(r).forEach(function(b){a+=r[b].stats.size});return a});return{create:function(d,e,g,l){function h(b){var d=35044,n=null,g=0,e=0,f=1;Array.isArray(b)||P(b)||fa(b)||
b instanceof ArrayBuffer?n=b:"number"===typeof b?g=b|0:b&&("data"in b&&(n=b.data),"usage"in b&&(d=mb[b.usage]),"type"in b&&(e=Ja[b.type]),"dimension"in b&&(f=b.dimension|0),"length"in b&&(g=b.length|0));p.bind();n?m(p,n,d,e,f,l):(g&&a.bufferData(p.type,g,d),p.dtype=e||5121,p.usage=d,p.dimension=f,p.byteLength=g);c.profile&&(p.stats.size=p.byteLength*ja[p.dtype]);return h}b.bufferCount++;var p=new f(e);r[p.id]=p;g||h(d);h._reglType="buffer";h._buffer=p;h.subdata=function(b,c){var d=(c||0)|0,n;p.bind();
if(P(b)||b instanceof ArrayBuffer)a.bufferSubData(p.type,d,b);else if(Array.isArray(b)){if(0<b.length)if("number"===typeof b[0]){var g=B.allocType(p.dtype,b.length);jb(g,b);a.bufferSubData(p.type,d,g);B.freeType(g)}else if(Array.isArray(b[0])||P(b[0]))n=lb(b),g=Ua(b,n,p.dtype),a.bufferSubData(p.type,d,g),B.freeType(g)}else if(fa(b)){n=b.shape;var e=b.stride,l=g=0,f=0,v=0;1===n.length?(g=n[0],l=1,f=e[0],v=0):2===n.length&&(g=n[0],l=n[1],f=e[0],v=e[1]);n=Array.isArray(b.data)?p.dtype:Ha(b.data);n=B.allocType(n,
g*l);kb(n,b.data,g,l,f,v,b.offset);a.bufferSubData(p.type,d,n);B.freeType(n)}return h};c.profile&&(h.stats=p.stats);h.destroy=function(){q(p)};return h},createStream:function(a,b){var c=k.pop();c||(c=new f(a));c.bind();m(c,b,35040,0,1,!1);return c},destroyStream:function(a){k.push(a)},clear:function(){T(r).forEach(q);k.forEach(q)},getBuffer:function(a){return a&&a._buffer instanceof f?a._buffer:null},restore:function(){T(r).forEach(function(b){b.buffer=a.createBuffer();a.bindBuffer(b.type,b.buffer);
a.bufferData(b.type,b.persistentData||b.byteLength,b.usage)})},_initBuffer:m}}function Kb(a,b,c,d){function f(a){this.id=u++;q[this.id]=this;this.buffer=a;this.primType=4;this.type=this.vertCount=0}function e(d,e,g,l,h,f,y){d.buffer.bind();var k;e?((k=y)||P(e)&&(!fa(e)||P(e.data))||(k=b.oes_element_index_uint?5125:5123),c._initBuffer(d.buffer,e,g,k,3)):(a.bufferData(34963,f,g),d.buffer.dtype=k||5121,d.buffer.usage=g,d.buffer.dimension=3,d.buffer.byteLength=f);k=y;if(!y){switch(d.buffer.dtype){case 5121:case 5120:k=
5121;break;case 5123:case 5122:k=5123;break;case 5125:case 5124:k=5125}d.buffer.dtype=k}d.type=k;e=h;0>e&&(e=d.buffer.byteLength,5123===k?e>>=1:5125===k&&(e>>=2));d.vertCount=e;e=l;0>l&&(e=4,l=d.buffer.dimension,1===l&&(e=0),2===l&&(e=1),3===l&&(e=4));d.primType=e}function m(a){d.elementsCount--;delete q[a.id];a.buffer.destroy();a.buffer=null}var q={},u=0,r={uint8:5121,uint16:5123};b.oes_element_index_uint&&(r.uint32=5125);f.prototype.bind=function(){this.buffer.bind()};var k=[];return{create:function(a,
b){function g(a){if(a)if("number"===typeof a)l(a),h.primType=4,h.vertCount=a|0,h.type=5121;else{var b=null,c=35044,d=-1,f=-1,n=0,k=0;if(Array.isArray(a)||P(a)||fa(a))b=a;else if("data"in a&&(b=a.data),"usage"in a&&(c=mb[a.usage]),"primitive"in a&&(d=Ka[a.primitive]),"count"in a&&(f=a.count|0),"type"in a&&(k=r[a.type]),"length"in a)n=a.length|0;else if(n=f,5123===k||5122===k)n*=2;else if(5125===k||5124===k)n*=4;e(h,b,c,d,f,n,k)}else l(),h.primType=4,h.vertCount=0,h.type=5121;return g}var l=c.create(null,
34963,!0),h=new f(l._buffer);d.elementsCount++;g(a);g._reglType="elements";g._elements=h;g.subdata=function(a,b){l.subdata(a,b);return g};g.destroy=function(){m(h)};return g},createStream:function(a){var b=k.pop();b||(b=new f(c.create(null,34963,!0,!1)._buffer));e(b,a,35040,-1,-1,0,0);return b},destroyStream:function(a){k.push(a)},getElements:function(a){return"function"===typeof a&&a._elements instanceof f?a._elements:null},clear:function(){T(q).forEach(m)}}}function ob(a){for(var b=B.allocType(5123,
a.length),c=0;c<a.length;++c)if(isNaN(a[c]))b[c]=65535;else if(Infinity===a[c])b[c]=31744;else if(-Infinity===a[c])b[c]=64512;else{pb[0]=a[c];var d=Lb[0],f=d>>>31<<15,e=(d<<1>>>24)-127,d=d>>13&1023;b[c]=-24>e?f:-14>e?f+(d+1024>>-14-e):15<e?f+31744:f+(e+15<<10)+d}return b}function qa(a){return Array.isArray(a)||P(a)}function ra(a){return"[object "+a+"]"}function qb(a){return Array.isArray(a)&&(0===a.length||"number"===typeof a[0])}function rb(a){return Array.isArray(a)&&0!==a.length&&qa(a[0])?!0:!1}
function R(a){return Object.prototype.toString.call(a)}function Va(a){if(!a)return!1;var b=R(a);return 0<=Mb.indexOf(b)?!0:qb(a)||rb(a)||fa(a)}function sb(a,b){36193===a.type?(a.data=ob(b),B.freeType(b)):a.data=b}function La(a,b,c,d,f,e){a="undefined"!==typeof G[a]?G[a]:U[a]*ya[b];e&&(a*=6);if(f){for(d=0;1<=c;)d+=a*c*c,c/=2;return d}return a*c*d}function Nb(a,b,c,d,f,e,m){function q(){this.format=this.internalformat=6408;this.type=5121;this.flipY=this.premultiplyAlpha=this.compressed=!1;this.unpackAlignment=
1;this.colorSpace=37444;this.channels=this.height=this.width=0}function u(a,b){a.internalformat=b.internalformat;a.format=b.format;a.type=b.type;a.compressed=b.compressed;a.premultiplyAlpha=b.premultiplyAlpha;a.flipY=b.flipY;a.unpackAlignment=b.unpackAlignment;a.colorSpace=b.colorSpace;a.width=b.width;a.height=b.height;a.channels=b.channels}function r(a,b){if("object"===typeof b&&b){"premultiplyAlpha"in b&&(a.premultiplyAlpha=b.premultiplyAlpha);"flipY"in b&&(a.flipY=b.flipY);"alignment"in b&&(a.unpackAlignment=
b.alignment);"colorSpace"in b&&(a.colorSpace=Ob[b.colorSpace]);"type"in b&&(a.type=W[b.type]);var c=a.width,d=a.height,e=a.channels,h=!1;"shape"in b?(c=b.shape[0],d=b.shape[1],3===b.shape.length&&(e=b.shape[2],h=!0)):("radius"in b&&(c=d=b.radius),"width"in b&&(c=b.width),"height"in b&&(d=b.height),"channels"in b&&(e=b.channels,h=!0));a.width=c|0;a.height=d|0;a.channels=e|0;c=!1;"format"in b&&(c=b.format,d=a.internalformat=Q[c],a.format=Ea[d],c in W&&!("type"in b)&&(a.type=W[c]),c in I&&(a.compressed=
!0),c=!0);!h&&c?a.channels=U[a.format]:h&&!c&&a.channels!==Na[a.format]&&(a.format=a.internalformat=Na[a.channels])}}function k(b){a.pixelStorei(37440,b.flipY);a.pixelStorei(37441,b.premultiplyAlpha);a.pixelStorei(37443,b.colorSpace);a.pixelStorei(3317,b.unpackAlignment)}function n(){q.call(this);this.yOffset=this.xOffset=0;this.data=null;this.needsFree=!1;this.element=null;this.needsCopy=!1}function t(a,b){var c=null;Va(b)?c=b:b&&(r(a,b),"x"in b&&(a.xOffset=b.x|0),"y"in b&&(a.yOffset=b.y|0),Va(b.data)&&
(c=b.data));if(b.copy){var d=f.viewportWidth,e=f.viewportHeight;a.width=a.width||d-a.xOffset;a.height=a.height||e-a.yOffset;a.needsCopy=!0}else if(!c)a.width=a.width||1,a.height=a.height||1,a.channels=a.channels||4;else if(P(c))a.channels=a.channels||4,a.data=c,"type"in b||5121!==a.type||(a.type=Ia[Object.prototype.toString.call(c)]|0);else if(qb(c)){a.channels=a.channels||4;d=c;e=d.length;switch(a.type){case 5121:case 5123:case 5125:case 5126:e=B.allocType(a.type,e);e.set(d);a.data=e;break;case 36193:a.data=
ob(d)}a.alignment=1;a.needsFree=!0}else if(fa(c)){d=c.data;Array.isArray(d)||5121!==a.type||(a.type=Ia[Object.prototype.toString.call(d)]|0);var e=c.shape,h=c.stride,g,l,v,k;3===e.length?(v=e[2],k=h[2]):k=v=1;g=e[0];l=e[1];e=h[0];h=h[1];a.alignment=1;a.width=g;a.height=l;a.channels=v;a.format=a.internalformat=Na[v];a.needsFree=!0;g=k;c=c.offset;v=a.width;k=a.height;l=a.channels;for(var n=B.allocType(36193===a.type?5126:a.type,v*k*l),x=0,F=0;F<k;++F)for(var aa=0;aa<v;++aa)for(var Fa=0;Fa<l;++Fa)n[x++]=
d[e*aa+h*F+g*Fa+c];sb(a,n)}else if(R(c)===Wa||R(c)===Xa||R(c)===ub)R(c)===Wa||R(c)===Xa?a.element=c:a.element=c.canvas,a.width=a.element.width,a.height=a.element.height,a.channels=4;else if(R(c)===vb)a.element=c,a.width=c.width,a.height=c.height,a.channels=4;else if(R(c)===wb)a.element=c,a.width=c.naturalWidth,a.height=c.naturalHeight,a.channels=4;else if(R(c)===xb)a.element=c,a.width=c.videoWidth,a.height=c.videoHeight,a.channels=4;else if(rb(c)){d=a.width||c[0].length;e=a.height||c.length;h=a.channels;
h=qa(c[0][0])?h||c[0][0].length:h||1;g=Pa.shape(c);v=1;for(k=0;k<g.length;++k)v*=g[k];v=B.allocType(36193===a.type?5126:a.type,v);Pa.flatten(c,g,"",v);sb(a,v);a.alignment=1;a.width=d;a.height=e;a.channels=h;a.format=a.internalformat=Na[h];a.needsFree=!0}}function g(b,c,e,h,g){var v=b.element,l=b.data,f=b.internalformat,n=b.format,y=b.type,w=b.width,x=b.height;k(b);v?a.texSubImage2D(c,g,e,h,n,y,v):b.compressed?a.compressedTexSubImage2D(c,g,e,h,f,w,x,l):b.needsCopy?(d(),a.copyTexSubImage2D(c,g,e,h,
b.xOffset,b.yOffset,w,x)):a.texSubImage2D(c,g,e,h,w,x,n,y,l)}function l(){return Qa.pop()||new n}function h(a){a.needsFree&&B.freeType(a.data);n.call(a);Qa.push(a)}function p(){q.call(this);this.genMipmaps=!1;this.mipmapHint=4352;this.mipmask=0;this.images=Array(16)}function y(a,b,c){var d=a.images[0]=l();a.mipmask=1;d.width=a.width=b;d.height=a.height=c;d.channels=a.channels=4}function C(a,b){var c=null;if(Va(b))c=a.images[0]=l(),u(c,a),t(c,b),a.mipmask=1;else if(r(a,b),Array.isArray(b.mipmap))for(var d=
b.mipmap,e=0;e<d.length;++e)c=a.images[e]=l(),u(c,a),c.width>>=e,c.height>>=e,t(c,d[e]),a.mipmask|=1<<e;else c=a.images[0]=l(),u(c,a),t(c,b),a.mipmask=1;u(a,a.images[0])}function w(b,c){for(var e=b.images,h=0;h<e.length&&e[h];++h){var g=e[h],v=c,l=h,f=g.element,n=g.data,y=g.internalformat,w=g.format,x=g.type,F=g.width,aa=g.height;k(g);f?a.texImage2D(v,l,w,w,x,f):g.compressed?a.compressedTexImage2D(v,l,y,F,aa,0,n):g.needsCopy?(d(),a.copyTexImage2D(v,l,w,g.xOffset,g.yOffset,F,aa,0)):a.texImage2D(v,
l,w,F,aa,0,w,x,n||null)}}function z(){var a=S.pop()||new p;q.call(a);for(var b=a.mipmask=0;16>b;++b)a.images[b]=null;return a}function wa(a){for(var b=a.images,c=0;c<b.length;++c)b[c]&&h(b[c]),b[c]=null;S.push(a)}function E(){this.magFilter=this.minFilter=9728;this.wrapT=this.wrapS=33071;this.anisotropic=1;this.genMipmaps=!1;this.mipmapHint=4352}function nb(a,b){"min"in b&&(a.minFilter=V[b.min],0<=Pb.indexOf(a.minFilter)&&!("faces"in b)&&(a.genMipmaps=!0));"mag"in b&&(a.magFilter=na[b.mag]);var c=
a.wrapS,d=a.wrapT;if("wrap"in b){var e=b.wrap;"string"===typeof e?c=d=oa[e]:Array.isArray(e)&&(c=oa[e[0]],d=oa[e[1]])}else"wrapS"in b&&(c=oa[b.wrapS]),"wrapT"in b&&(d=oa[b.wrapT]);a.wrapS=c;a.wrapT=d;"anisotropic"in b&&(a.anisotropic=b.anisotropic);if("mipmap"in b){c=!1;switch(typeof b.mipmap){case "string":a.mipmapHint=J[b.mipmap];c=a.genMipmaps=!0;break;case "boolean":c=a.genMipmaps=b.mipmap;break;case "object":a.genMipmaps=!1,c=!0}!c||"min"in b||(a.minFilter=9984)}}function D(c,d){a.texParameteri(d,
10241,c.minFilter);a.texParameteri(d,10240,c.magFilter);a.texParameteri(d,10242,c.wrapS);a.texParameteri(d,10243,c.wrapT);b.ext_texture_filter_anisotropic&&a.texParameteri(d,34046,c.anisotropic);c.genMipmaps&&(a.hint(33170,c.mipmapHint),a.generateMipmap(d))}function v(b){q.call(this);this.mipmask=0;this.internalformat=6408;this.id=Z++;this.refCount=1;this.target=b;this.texture=a.createTexture();this.unit=-1;this.bindCount=0;this.texInfo=new E;m.profile&&(this.stats={size:0})}function ba(b){a.activeTexture(33984);
a.bindTexture(b.target,b.texture)}function za(){var b=X[0];b?a.bindTexture(b.target,b.texture):a.bindTexture(3553,null)}function A(b){var c=b.texture,d=b.unit,h=b.target;0<=d&&(a.activeTexture(33984+d),a.bindTexture(h,null),X[d]=null);a.deleteTexture(c);b.texture=null;b.params=null;b.pixels=null;b.refCount=0;delete ka[b.id];e.textureCount--}var J={"don't care":4352,"dont care":4352,nice:4354,fast:4353},oa={repeat:10497,clamp:33071,mirror:33648},na={nearest:9728,linear:9729},V=O({mipmap:9987,"nearest mipmap nearest":9984,
"linear mipmap nearest":9985,"nearest mipmap linear":9986,"linear mipmap linear":9987},na),Ob={none:0,browser:37444},W={uint8:5121,rgba4:32819,rgb565:33635,"rgb5 a1":32820},Q={alpha:6406,luminance:6409,"luminance alpha":6410,rgb:6407,rgba:6408,rgba4:32854,"rgb5 a1":32855,rgb565:36194},I={};b.ext_srgb&&(Q.srgb=35904,Q.srgba=35906);b.oes_texture_float&&(W.float32=W["float"]=5126);b.oes_texture_half_float&&(W.float16=W["half float"]=36193);b.webgl_depth_texture&&(O(Q,{depth:6402,"depth stencil":34041}),
O(W,{uint16:5123,uint32:5125,"depth stencil":34042}));b.webgl_compressed_texture_s3tc&&O(I,{"rgb s3tc dxt1":33776,"rgba s3tc dxt1":33777,"rgba s3tc dxt3":33778,"rgba s3tc dxt5":33779});b.webgl_compressed_texture_atc&&O(I,{"rgb atc":35986,"rgba atc explicit alpha":35987,"rgba atc interpolated alpha":34798});b.webgl_compressed_texture_pvrtc&&O(I,{"rgb pvrtc 4bppv1":35840,"rgb pvrtc 2bppv1":35841,"rgba pvrtc 4bppv1":35842,"rgba pvrtc 2bppv1":35843});b.webgl_compressed_texture_etc1&&(I["rgb etc1"]=36196);
var K=Array.prototype.slice.call(a.getParameter(34467));Object.keys(I).forEach(function(a){var b=I[a];0<=K.indexOf(b)&&(Q[a]=b)});var G=Object.keys(Q);c.textureFormats=G;var H=[];Object.keys(Q).forEach(function(a){H[Q[a]]=a});var xa=[];Object.keys(W).forEach(function(a){xa[W[a]]=a});var L=[];Object.keys(na).forEach(function(a){L[na[a]]=a});var pa=[];Object.keys(V).forEach(function(a){pa[V[a]]=a});var ga=[];Object.keys(oa).forEach(function(a){ga[oa[a]]=a});var Ea=G.reduce(function(a,c){var d=Q[c];
6409===d||6406===d||6409===d||6410===d||6402===d||34041===d||b.ext_srgb&&(35904===d||35906===d)?a[d]=d:32855===d||0<=c.indexOf("rgba")?a[d]=6408:a[d]=6407;return a},{}),Qa=[],S=[],Z=0,ka={},ha=c.maxTextureUnits,X=Array(ha).map(function(){return null});O(v.prototype,{bind:function(){this.bindCount+=1;var b=this.unit;if(0>b){for(var c=0;c<ha;++c){var d=X[c];if(d){if(0<d.bindCount)continue;d.unit=-1}X[c]=this;b=c;break}m.profile&&e.maxTextureUnits<b+1&&(e.maxTextureUnits=b+1);this.unit=b;a.activeTexture(33984+
b);a.bindTexture(this.target,this.texture)}return b},unbind:function(){--this.bindCount},decRef:function(){0>=--this.refCount&&A(this)}});m.profile&&(e.getTotalTextureSize=function(){var a=0;Object.keys(ka).forEach(function(b){a+=ka[b].stats.size});return a});return{create2D:function(b,c){function d(a,b){var c=f.texInfo;E.call(c);var e=z();"number"===typeof a?"number"===typeof b?y(e,a|0,b|0):y(e,a|0,a|0):a?(nb(c,a),C(e,a)):y(e,1,1);c.genMipmaps&&(e.mipmask=(e.width<<1)-1);f.mipmask=e.mipmask;u(f,
e);f.internalformat=e.internalformat;d.width=e.width;d.height=e.height;ba(f);w(e,3553);D(c,3553);za();wa(e);m.profile&&(f.stats.size=La(f.internalformat,f.type,e.width,e.height,c.genMipmaps,!1));d.format=H[f.internalformat];d.type=xa[f.type];d.mag=L[c.magFilter];d.min=pa[c.minFilter];d.wrapS=ga[c.wrapS];d.wrapT=ga[c.wrapT];return d}var f=new v(3553);ka[f.id]=f;e.textureCount++;d(b,c);d.subimage=function(a,b,c,e){b|=0;c|=0;e|=0;var v=l();u(v,f);v.width=0;v.height=0;t(v,a);v.width=v.width||(f.width>>
e)-b;v.height=v.height||(f.height>>e)-c;ba(f);g(v,3553,b,c,e);za();h(v);return d};d.resize=function(b,c){var e=b|0,h=c|0||e;if(e===f.width&&h===f.height)return d;d.width=f.width=e;d.height=f.height=h;ba(f);for(var g=0;f.mipmask>>g;++g){var v=e>>g,l=h>>g;if(!v||!l)break;a.texImage2D(3553,g,f.format,v,l,0,f.format,f.type,null)}za();m.profile&&(f.stats.size=La(f.internalformat,f.type,e,h,!1,!1));return d};d._reglType="texture2d";d._texture=f;m.profile&&(d.stats=f.stats);d.destroy=function(){f.decRef()};
return d},createCube:function(b,c,d,f,k,n){function p(a,b,c,d,e,h){var f,g=A.texInfo;E.call(g);for(f=0;6>f;++f)J[f]=z();if("number"===typeof a||!a)for(a=a|0||1,f=0;6>f;++f)y(J[f],a,a);else if("object"===typeof a)if(b)C(J[0],a),C(J[1],b),C(J[2],c),C(J[3],d),C(J[4],e),C(J[5],h);else if(nb(g,a),r(A,a),"faces"in a)for(a=a.faces,f=0;6>f;++f)u(J[f],A),C(J[f],a[f]);else for(f=0;6>f;++f)C(J[f],a);u(A,J[0]);A.mipmask=g.genMipmaps?(J[0].width<<1)-1:J[0].mipmask;A.internalformat=J[0].internalformat;p.width=
J[0].width;p.height=J[0].height;ba(A);for(f=0;6>f;++f)w(J[f],34069+f);D(g,34067);za();m.profile&&(A.stats.size=La(A.internalformat,A.type,p.width,p.height,g.genMipmaps,!0));p.format=H[A.internalformat];p.type=xa[A.type];p.mag=L[g.magFilter];p.min=pa[g.minFilter];p.wrapS=ga[g.wrapS];p.wrapT=ga[g.wrapT];for(f=0;6>f;++f)wa(J[f]);return p}var A=new v(34067);ka[A.id]=A;e.cubeCount++;var J=Array(6);p(b,c,d,f,k,n);p.subimage=function(a,b,c,d,e){c|=0;d|=0;e|=0;var f=l();u(f,A);f.width=0;f.height=0;t(f,b);
f.width=f.width||(A.width>>e)-c;f.height=f.height||(A.height>>e)-d;ba(A);g(f,34069+a,c,d,e);za();h(f);return p};p.resize=function(b){b|=0;if(b!==A.width){p.width=A.width=b;p.height=A.height=b;ba(A);for(var c=0;6>c;++c)for(var x=0;A.mipmask>>x;++x)a.texImage2D(34069+c,x,A.format,b>>x,b>>x,0,A.format,A.type,null);za();m.profile&&(A.stats.size=La(A.internalformat,A.type,p.width,p.height,!1,!0));return p}};p._reglType="textureCube";p._texture=A;m.profile&&(p.stats=A.stats);p.destroy=function(){A.decRef()};
return p},clear:function(){for(var b=0;b<ha;++b)a.activeTexture(33984+b),a.bindTexture(3553,null),X[b]=null;T(ka).forEach(A);e.cubeCount=0;e.textureCount=0},getTexture:function(a){return null},restore:function(){for(var b=0;b<ha;++b){var c=X[b];c&&(c.bindCount=0,c.unit=-1,X[b]=null)}T(ka).forEach(function(b){b.texture=a.createTexture();a.bindTexture(b.target,b.texture);for(var c=0;32>c;++c)if(0!==(b.mipmask&1<<c))if(3553===b.target)a.texImage2D(3553,c,b.internalformat,b.width>>c,b.height>>c,0,b.internalformat,
b.type,null);else for(var d=0;6>d;++d)a.texImage2D(34069+d,c,b.internalformat,b.width>>c,b.height>>c,0,b.internalformat,b.type,null);D(b.texInfo,b.target)})},refresh:function(){for(var b=0;b<ha;++b){var c=X[b];c&&(c.bindCount=0,c.unit=-1,X[b]=null);a.activeTexture(33984+b);a.bindTexture(3553,null);a.bindTexture(34067,null)}}}}function Qb(a,b,c,d,f,e){function m(a,b,c){this.target=a;this.texture=b;this.renderbuffer=c;var d=a=0;b?(a=b.width,d=b.height):c&&(a=c.width,d=c.height);this.width=a;this.height=
d}function q(a){a&&(a.texture&&a.texture._texture.decRef(),a.renderbuffer&&a.renderbuffer._renderbuffer.decRef())}function u(a,b,c){a&&(a.texture?a.texture._texture.refCount+=1:a.renderbuffer._renderbuffer.refCount+=1)}function r(b,c){c&&(c.texture?a.framebufferTexture2D(36160,b,c.target,c.texture._texture.texture,0):a.framebufferRenderbuffer(36160,b,36161,c.renderbuffer._renderbuffer.renderbuffer))}function k(a){var b=3553,c=null,d=null,e=a;"object"===typeof a&&(e=a.data,"target"in a&&(b=a.target|
0));a=e._reglType;"texture2d"===a?c=e:"textureCube"===a?c=e:"renderbuffer"===a&&(d=e,b=36161);return new m(b,c,d)}function n(a,b,c,e,g){if(c)return a=d.create2D({width:a,height:b,format:e,type:g}),a._texture.refCount=0,new m(3553,a,null);a=f.create({width:a,height:b,format:e});a._renderbuffer.refCount=0;return new m(36161,null,a)}function t(a){return a&&(a.texture||a.renderbuffer)}function g(a,b,c){a&&(a.texture?a.texture.resize(b,c):a.renderbuffer&&a.renderbuffer.resize(b,c),a.width=b,a.height=c)}
function l(){this.id=B++;D[this.id]=this;this.framebuffer=a.createFramebuffer();this.height=this.width=0;this.colorAttachments=[];this.depthStencilAttachment=this.stencilAttachment=this.depthAttachment=null}function h(a){a.colorAttachments.forEach(q);q(a.depthAttachment);q(a.stencilAttachment);q(a.depthStencilAttachment)}function p(b){a.deleteFramebuffer(b.framebuffer);b.framebuffer=null;e.framebufferCount--;delete D[b.id]}function y(b){var d;a.bindFramebuffer(36160,b.framebuffer);var e=b.colorAttachments;
for(d=0;d<e.length;++d)r(36064+d,e[d]);for(d=e.length;d<c.maxColorAttachments;++d)a.framebufferTexture2D(36160,36064+d,3553,null,0);a.framebufferTexture2D(36160,33306,3553,null,0);a.framebufferTexture2D(36160,36096,3553,null,0);a.framebufferTexture2D(36160,36128,3553,null,0);r(36096,b.depthAttachment);r(36128,b.stencilAttachment);r(33306,b.depthStencilAttachment);a.checkFramebufferStatus(36160);a.isContextLost();a.bindFramebuffer(36160,w.next?w.next.framebuffer:null);w.cur=w.next;a.getError()}function C(a,
b){function c(a,b){var e,f=0,g=0,l=!0,w=!0;e=null;var p=!0,v="rgba",m="uint8",r=1,C=null,q=null,pa=null,ga=!1;if("number"===typeof a)f=a|0,g=b|0||f;else if(a){"shape"in a?(g=a.shape,f=g[0],g=g[1]):("radius"in a&&(f=g=a.radius),"width"in a&&(f=a.width),"height"in a&&(g=a.height));if("color"in a||"colors"in a)e=a.color||a.colors,Array.isArray(e);if(!e){"colorCount"in a&&(r=a.colorCount|0);"colorTexture"in a&&(p=!!a.colorTexture,v="rgba4");if("colorType"in a&&(m=a.colorType,!p))if("half float"===m||
"float16"===m)v="rgba16f";else if("float"===m||"float32"===m)v="rgba32f";"colorFormat"in a&&(v=a.colorFormat,0<=z.indexOf(v)?p=!0:0<=wa.indexOf(v)&&(p=!1))}if("depthTexture"in a||"depthStencilTexture"in a)ga=!(!a.depthTexture&&!a.depthStencilTexture);"depth"in a&&("boolean"===typeof a.depth?l=a.depth:(C=a.depth,w=!1));"stencil"in a&&("boolean"===typeof a.stencil?w=a.stencil:(q=a.stencil,l=!1));"depthStencil"in a&&("boolean"===typeof a.depthStencil?l=w=a.depthStencil:(pa=a.depthStencil,w=l=!1))}else f=
g=1;var D=null,ba=null,E=null,B=null;if(Array.isArray(e))D=e.map(k);else if(e)D=[k(e)];else for(D=Array(r),e=0;e<r;++e)D[e]=n(f,g,p,v,m);f=f||D[0].width;g=g||D[0].height;C?ba=k(C):l&&!w&&(ba=n(f,g,ga,"depth","uint32"));q?E=k(q):w&&!l&&(E=n(f,g,!1,"stencil","uint8"));pa?B=k(pa):!C&&!q&&w&&l&&(B=n(f,g,ga,"depth stencil","depth stencil"));l=null;for(e=0;e<D.length;++e)u(D[e],f,g),D[e]&&D[e].texture&&(w=Ya[D[e].texture._texture.format]*Ra[D[e].texture._texture.type],null===l&&(l=w));u(ba,f,g);u(E,f,g);
u(B,f,g);h(d);d.width=f;d.height=g;d.colorAttachments=D;d.depthAttachment=ba;d.stencilAttachment=E;d.depthStencilAttachment=B;c.color=D.map(t);c.depth=t(ba);c.stencil=t(E);c.depthStencil=t(B);c.width=d.width;c.height=d.height;y(d);return c}var d=new l;e.framebufferCount++;c(a,b);return O(c,{resize:function(a,b){var e=Math.max(a|0,1),f=Math.max(b|0||e,1);if(e===d.width&&f===d.height)return c;for(var h=d.colorAttachments,l=0;l<h.length;++l)g(h[l],e,f);g(d.depthAttachment,e,f);g(d.stencilAttachment,
e,f);g(d.depthStencilAttachment,e,f);d.width=c.width=e;d.height=c.height=f;y(d);return c},_reglType:"framebuffer",_framebuffer:d,destroy:function(){p(d);h(d)},use:function(a){w.setFBO({framebuffer:c},a)}})}var w={cur:null,next:null,dirty:!1,setFBO:null},z=["rgba"],wa=["rgba4","rgb565","rgb5 a1"];b.ext_srgb&&wa.push("srgba");b.ext_color_buffer_half_float&&wa.push("rgba16f","rgb16f");b.webgl_color_buffer_float&&wa.push("rgba32f");var E=["uint8"];b.oes_texture_half_float&&E.push("half float","float16");
b.oes_texture_float&&E.push("float","float32");var B=0,D={};return O(w,{getFramebuffer:function(a){return"function"===typeof a&&"framebuffer"===a._reglType&&(a=a._framebuffer,a instanceof l)?a:null},create:C,createCube:function(a){function b(a){var e,f={color:null},g=0,h=null;e="rgba";var l="uint8",k=1;if("number"===typeof a)g=a|0;else if(a){"shape"in a?g=a.shape[0]:("radius"in a&&(g=a.radius|0),"width"in a?g=a.width|0:"height"in a&&(g=a.height|0));if("color"in a||"colors"in a)h=a.color||a.colors,
Array.isArray(h);h||("colorCount"in a&&(k=a.colorCount|0),"colorType"in a&&(l=a.colorType),"colorFormat"in a&&(e=a.colorFormat));"depth"in a&&(f.depth=a.depth);"stencil"in a&&(f.stencil=a.stencil);"depthStencil"in a&&(f.depthStencil=a.depthStencil)}else g=1;if(h)if(Array.isArray(h))for(a=[],e=0;e<h.length;++e)a[e]=h[e];else a=[h];else for(a=Array(k),h={radius:g,format:e,type:l},e=0;e<k;++e)a[e]=d.createCube(h);f.color=Array(a.length);for(e=0;e<a.length;++e)k=a[e],g=g||k.width,f.color[e]={target:34069,
data:a[e]};for(e=0;6>e;++e){for(k=0;k<a.length;++k)f.color[k].target=34069+e;0<e&&(f.depth=c[0].depth,f.stencil=c[0].stencil,f.depthStencil=c[0].depthStencil);if(c[e])c[e](f);else c[e]=C(f)}return O(b,{width:g,height:g,color:a})}var c=Array(6);b(a);return O(b,{faces:c,resize:function(a){var d=a|0;if(d===b.width)return b;var e=b.color;for(a=0;a<e.length;++a)e[a].resize(d);for(a=0;6>a;++a)c[a].resize(d);b.width=b.height=d;return b},_reglType:"framebufferCube",destroy:function(){c.forEach(function(a){a.destroy()})}})},
clear:function(){T(D).forEach(p)},restore:function(){w.cur=null;w.next=null;w.dirty=!0;T(D).forEach(function(b){b.framebuffer=a.createFramebuffer();y(b)})}})}function Za(){this.w=this.z=this.y=this.x=this.state=0;this.buffer=null;this.size=0;this.normalized=!1;this.type=5126;this.divisor=this.stride=this.offset=0}function Rb(a,b,c,d,f,e,m){function q(a){if(a!==p.currentVAO){var c=b.oes_vertex_array_object;a?c.bindVertexArrayOES(a.vao):c.bindVertexArrayOES(null);p.currentVAO=a}}function u(c){if(c!==
p.currentVAO){if(c)c.bindAttrs();else{for(var d=b.angle_instanced_arrays,e=0;e<g.length;++e){var f=g[e];f.buffer?(a.enableVertexAttribArray(e),f.buffer.bind(),a.vertexAttribPointer(e,f.size,f.type,f.normalized,f.stride,f.offfset),d&&f.divisor&&d.vertexAttribDivisorANGLE(e,f.divisor)):(a.disableVertexAttribArray(e),a.vertexAttrib4f(e,f.x,f.y,f.z,f.w))}m.elements?a.bindBuffer(34963,m.elements.buffer.buffer):a.bindBuffer(34963,null)}p.currentVAO=c}}function r(){T(h).forEach(function(a){a.destroy()})}
function k(){this.id=++l;this.attributes=[];this.elements=null;this.ownsElements=!1;this.offset=this.count=0;this.instances=-1;this.primitive=4;var a=b.oes_vertex_array_object;this.vao=a?a.createVertexArrayOES():null;h[this.id]=this;this.buffers=[]}function n(){b.oes_vertex_array_object&&T(h).forEach(function(a){a.refresh()})}var t=c.maxAttributes,g=Array(t);for(c=0;c<t;++c)g[c]=new Za;var l=0,h={},p={Record:Za,scope:{},state:g,currentVAO:null,targetVAO:null,restore:b.oes_vertex_array_object?n:function(){},
createVAO:function(a){function b(a){var d;Array.isArray(a)?(d=a,c.elements&&c.ownsElements&&c.elements.destroy(),c.elements=null,c.ownsElements=!1,c.offset=0,c.count=0,c.instances=-1,c.primitive=4):(a.elements?(d=a.elements,c.ownsElements?("function"===typeof d&&"elements"===d._reglType?c.elements.destroy():c.elements(d),c.ownsElements=!1):e.getElements(a.elements)?(c.elements=a.elements,c.ownsElements=!1):(c.elements=e.create(a.elements),c.ownsElements=!0)):(c.elements=null,c.ownsElements=!1),d=
a.attributes,c.offset=0,c.count=-1,c.instances=-1,c.primitive=4,c.elements&&(c.count=c.elements._elements.vertCount,c.primitive=c.elements._elements.primType),"offset"in a&&(c.offset=a.offset|0),"count"in a&&(c.count=a.count|0),"instances"in a&&(c.instances=a.instances|0),"primitive"in a&&(c.primitive=Ka[a.primitive]));a={};var g=c.attributes;g.length=d.length;for(var h=0;h<d.length;++h){var l=d[h],k=g[h]=new Za,n=l.data||l;if(Array.isArray(n)||P(n)||fa(n)){var p;c.buffers[h]&&(p=c.buffers[h],P(n)&&
p._buffer.byteLength>=n.byteLength?p.subdata(n):(p.destroy(),c.buffers[h]=null));c.buffers[h]||(p=c.buffers[h]=f.create(l,34962,!1,!0));k.buffer=f.getBuffer(p);k.size=k.buffer.dimension|0;k.normalized=!1;k.type=k.buffer.dtype;k.offset=0;k.stride=0;k.divisor=0;k.state=1;a[h]=1}else f.getBuffer(l)?(k.buffer=f.getBuffer(l),k.size=k.buffer.dimension|0,k.normalized=!1,k.type=k.buffer.dtype,k.offset=0,k.stride=0,k.divisor=0,k.state=1):f.getBuffer(l.buffer)?(k.buffer=f.getBuffer(l.buffer),k.size=(+l.size||
k.buffer.dimension)|0,k.normalized=!!l.normalized||!1,k.type="type"in l?Ja[l.type]:k.buffer.dtype,k.offset=(l.offset||0)|0,k.stride=(l.stride||0)|0,k.divisor=(l.divisor||0)|0,k.state=1):"x"in l&&(k.x=+l.x||0,k.y=+l.y||0,k.z=+l.z||0,k.w=+l.w||0,k.state=2)}for(p=0;p<c.buffers.length;++p)!a[p]&&c.buffers[p]&&(c.buffers[p].destroy(),c.buffers[p]=null);c.refresh();return b}var c=new k;d.vaoCount+=1;b.destroy=function(){for(var a=0;a<c.buffers.length;++a)c.buffers[a]&&c.buffers[a].destroy();c.buffers.length=
0;c.ownsElements&&(c.elements.destroy(),c.elements=null,c.ownsElements=!1);c.destroy()};b._vao=c;b._reglType="vao";return b(a)},getVAO:function(a){return"function"===typeof a&&a._vao?a._vao:null},destroyBuffer:function(b){for(var c=0;c<g.length;++c){var d=g[c];d.buffer===b&&(a.disableVertexAttribArray(c),d.buffer=null)}},setVAO:b.oes_vertex_array_object?q:u,clear:b.oes_vertex_array_object?r:function(){}};k.prototype.bindAttrs=function(){for(var c=b.angle_instanced_arrays,d=this.attributes,f=0;f<d.length;++f){var g=
d[f];g.buffer?(a.enableVertexAttribArray(f),a.bindBuffer(34962,g.buffer.buffer),a.vertexAttribPointer(f,g.size,g.type,g.normalized,g.stride,g.offset),c&&g.divisor&&c.vertexAttribDivisorANGLE(f,g.divisor)):(a.disableVertexAttribArray(f),a.vertexAttrib4f(f,g.x,g.y,g.z,g.w))}for(c=d.length;c<t;++c)a.disableVertexAttribArray(c);(c=e.getElements(this.elements))?a.bindBuffer(34963,c.buffer.buffer):a.bindBuffer(34963,null)};k.prototype.refresh=function(){var a=b.oes_vertex_array_object;a&&(a.bindVertexArrayOES(this.vao),
this.bindAttrs(),p.currentVAO=null,a.bindVertexArrayOES(null))};k.prototype.destroy=function(){if(this.vao){var a=b.oes_vertex_array_object;this===p.currentVAO&&(p.currentVAO=null,a.bindVertexArrayOES(null));a.deleteVertexArrayOES(this.vao);this.vao=null}this.ownsElements&&(this.elements.destroy(),this.elements=null,this.ownsElements=!1);h[this.id]&&(delete h[this.id],--d.vaoCount)};return p}function Sb(a,b,c,d){function f(a,b,c,d){this.name=a;this.id=b;this.location=c;this.info=d}function e(a,b){for(var c=
0;c<a.length;++c)if(a[c].id===b.id){a[c].location=b.location;return}a.push(b)}function m(c,d,e){e=35632===c?r:k;var f=e[d];if(!f){var g=b.str(d),f=a.createShader(c);a.shaderSource(f,g);a.compileShader(f);e[d]=f}return f}function q(a,b){this.id=g++;this.fragId=a;this.vertId=b;this.program=null;this.uniforms=[];this.attributes=[];this.refCount=1;d.profile&&(this.stats={uniformsCount:0,attributesCount:0})}function u(c,g,k){var n;n=m(35632,c.fragId);var t=m(35633,c.vertId);g=c.program=a.createProgram();
a.attachShader(g,n);a.attachShader(g,t);if(k)for(n=0;n<k.length;++n)t=k[n],a.bindAttribLocation(g,t[0],t[1]);a.linkProgram(g);t=a.getProgramParameter(g,35718);d.profile&&(c.stats.uniformsCount=t);var r=c.uniforms;for(n=0;n<t;++n)if(k=a.getActiveUniform(g,n))if(1<k.size)for(var q=0;q<k.size;++q){var u=k.name.replace("[0]","["+q+"]");e(r,new f(u,b.id(u),a.getUniformLocation(g,u),k))}else e(r,new f(k.name,b.id(k.name),a.getUniformLocation(g,k.name),k));t=a.getProgramParameter(g,35721);d.profile&&(c.stats.attributesCount=
t);c=c.attributes;for(n=0;n<t;++n)(k=a.getActiveAttrib(g,n))&&e(c,new f(k.name,b.id(k.name),a.getAttribLocation(g,k.name),k))}var r={},k={},n={},t=[],g=0;d.profile&&(c.getMaxUniformsCount=function(){var a=0;t.forEach(function(b){b.stats.uniformsCount>a&&(a=b.stats.uniformsCount)});return a},c.getMaxAttributesCount=function(){var a=0;t.forEach(function(b){b.stats.attributesCount>a&&(a=b.stats.attributesCount)});return a});return{clear:function(){var b=a.deleteShader.bind(a);T(r).forEach(b);r={};T(k).forEach(b);
k={};t.forEach(function(b){a.deleteProgram(b.program)});t.length=0;n={};c.shaderCount=0},program:function(b,d,e,f){var g=n[d];g||(g=n[d]={});var m=g[b];if(m&&(m.refCount++,!f))return m;var z=new q(d,b);c.shaderCount++;u(z,e,f);m||(g[b]=z);t.push(z);return O(z,{destroy:function(){z.refCount--;if(0>=z.refCount){a.deleteProgram(z.program);var b=t.indexOf(z);t.splice(b,1);c.shaderCount--}0>=g[z.vertId].refCount&&(a.deleteShader(k[z.vertId]),delete k[z.vertId],delete n[z.fragId][z.vertId]);Object.keys(n[z.fragId]).length||
(a.deleteShader(r[z.fragId]),delete r[z.fragId],delete n[z.fragId])}})},restore:function(){r={};k={};for(var a=0;a<t.length;++a)u(t[a],null,t[a].attributes.map(function(a){return[a.location,a.name]}))},shader:m,frag:-1,vert:-1}}function Tb(a,b,c,d,f,e,m){function q(e){var f;f=null===b.next?5121:b.next.colorAttachments[0].texture._texture.type;var n=0,t=0,g=d.framebufferWidth,l=d.framebufferHeight,h=null;P(e)?h=e:e&&(n=e.x|0,t=e.y|0,g=(e.width||d.framebufferWidth-n)|0,l=(e.height||d.framebufferHeight-
t)|0,h=e.data||null);c();e=g*l*4;h||(5121===f?h=new Uint8Array(e):5126===f&&(h=h||new Float32Array(e)));a.pixelStorei(3333,4);a.readPixels(n,t,g,l,6408,f,h);return h}function u(a){var c;b.setFBO({framebuffer:a.framebuffer},function(){c=q(a)});return c}return function(a){return a&&"framebuffer"in a?u(a):q(a)}}function Ub(a){for(var b=Array(a.length>>2),c=0;c<b.length;c++)b[c]=0;for(c=0;c<8*a.length;c+=8)b[c>>5]|=(a.charCodeAt(c/8)&255)<<24-c%32;var d=8*a.length;a=[1779033703,-1150833019,1013904242,
-1521486534,1359893119,-1694144372,528734635,1541459225];var c=Array(64),f,e,m,q,u,r,k,n,t,g,l;b[d>>5]|=128<<24-d%32;b[(d+64>>9<<4)+15]=d;for(n=0;n<b.length;n+=16){d=a[0];f=a[1];e=a[2];m=a[3];q=a[4];u=a[5];r=a[6];k=a[7];for(t=0;64>t;t++){if(16>t)c[t]=b[t+n];else{g=t;l=c[t-2];l=Y(l,17)^Y(l,19)^l>>>10;l=H(l,c[t-7]);var h;h=c[t-15];h=Y(h,7)^Y(h,18)^h>>>3;c[g]=H(H(l,h),c[t-16])}g=q;g=Y(g,6)^Y(g,11)^Y(g,25);g=H(H(H(H(k,g),q&u^~q&r),Vb[t]),c[t]);k=d;k=Y(k,2)^Y(k,13)^Y(k,22);l=H(k,d&f^d&e^f&e);k=r;r=u;u=
q;q=H(m,g);m=e;e=f;f=d;d=H(g,l)}a[0]=H(d,a[0]);a[1]=H(f,a[1]);a[2]=H(e,a[2]);a[3]=H(m,a[3]);a[4]=H(q,a[4]);a[5]=H(u,a[5]);a[6]=H(r,a[6]);a[7]=H(k,a[7])}b="";for(c=0;c<32*a.length;c+=8)b+=String.fromCharCode(a[c>>5]>>>24-c%32&255);return b}function Wb(a){for(var b="",c,d=0;d<a.length;d++)c=a.charCodeAt(d),b+="0123456789abcdef".charAt(c>>>4&15)+"0123456789abcdef".charAt(c&15);return b}function Xb(a){for(var b="",c=-1,d,f;++c<a.length;)d=a.charCodeAt(c),f=c+1<a.length?a.charCodeAt(c+1):0,55296<=d&&56319>=
d&&56320<=f&&57343>=f&&(d=65536+((d&1023)<<10)+(f&1023),c++),127>=d?b+=String.fromCharCode(d):2047>=d?b+=String.fromCharCode(192|d>>>6&31,128|d&63):65535>=d?b+=String.fromCharCode(224|d>>>12&15,128|d>>>6&63,128|d&63):2097151>=d&&(b+=String.fromCharCode(240|d>>>18&7,128|d>>>12&63,128|d>>>6&63,128|d&63));return b}function Y(a,b){return a>>>b|a<<32-b}function H(a,b){var c=(a&65535)+(b&65535);return(a>>16)+(b>>16)+(c>>16)<<16|c&65535}function Aa(a){return Array.prototype.slice.call(a)}function Ba(a){return Aa(a).join("")}
function Yb(a){function b(){var a=[],b=[];return O(function(){a.push.apply(a,Aa(arguments))},{def:function(){var c="v"+f++;b.push(c);0<arguments.length&&(a.push(c,"="),a.push.apply(a,Aa(arguments)),a.push(";"));return c},toString:function(){return Ba([0<b.length?"var "+b.join(",")+";":"",Ba(a)])}})}function c(){function a(b,e){d(b,e,"=",c.def(b,e),";")}var c=b(),d=b(),e=c.toString,f=d.toString;return O(function(){c.apply(c,Aa(arguments))},{def:c.def,entry:c,exit:d,save:a,set:function(b,d,e){a(b,d);
c(b,d,"=",e,";")},toString:function(){return e()+f()}})}var d=a&&a.cache,f=0,e=[],m=[],q=[],u=b(),r={};return{global:u,link:function(a,b){var c=b&&b.stable;if(!c)for(var d=0;d<m.length;++d)if(m[d]===a&&!q[d])return e[d];d="g"+f++;e.push(d);m.push(a);q.push(c);return d},block:b,proc:function(a,b){function d(){var a="a"+e.length;e.push(a);return a}var e=[];b=b||0;for(var f=0;f<b;++f)d();var f=c(),h=f.toString;return r[a]=O(f,{arg:d,toString:function(){return Ba(["function(",e.join(),"){",h(),"}"])}})},
scope:c,cond:function(){var a=Ba(arguments),b=c(),d=c(),e=b.toString,f=d.toString;return O(b,{then:function(){b.apply(b,Aa(arguments));return this},"else":function(){d.apply(d,Aa(arguments));return this},toString:function(){var b=f();b&&(b="else{"+b+"}");return Ba(["if(",a,"){",e(),"}",b])}})},compile:function(){var a=['"use strict";',u,"return {"];Object.keys(r).forEach(function(b){a.push('"',b,'":',r[b].toString(),",")});a.push("}");var b=Ba(a).replace(/;/g,";\n").replace(/}/g,"}\n").replace(/{/g,
"{\n"),c;if(d&&(c=Wb(Ub(Xb(b))),d[c]))return d[c].apply(null,m);b=Function.apply(null,e.concat(b));d&&(d[c]=b);return b.apply(null,m)}}}function Sa(a){return Array.isArray(a)||P(a)||fa(a)}function yb(a){return a.sort(function(a,c){return"viewport"===a?-1:"viewport"===c?1:a<c?-1:1})}function K(a,b,c,d){this.thisDep=a;this.contextDep=b;this.propDep=c;this.append=d}function la(a){return a&&!(a.thisDep||a.contextDep||a.propDep)}function E(a){return new K(!1,!1,!1,a)}function L(a,b){var c=a.type;if(0===
c)return c=a.data.length,new K(!0,1<=c,2<=c,b);if(4===c)return c=a.data,new K(c.thisDep,c.contextDep,c.propDep,b);if(5===c)return new K(!1,!1,!1,b);if(6===c){for(var d=c=!1,f=!1,e=0;e<a.data.length;++e){var m=a.data[e];1===m.type?f=!0:2===m.type?d=!0:3===m.type?c=!0:0===m.type?(c=!0,m=m.data,1<=m&&(d=!0),2<=m&&(f=!0)):4===m.type&&(c=c||m.data.thisDep,d=d||m.data.contextDep,f=f||m.data.propDep)}return new K(c,d,f,b)}return new K(3===c,2===c,1===c,b)}function Zb(a,b,c,d,f,e,m,q,u,r,k,n,t,g,l,h){function p(a){return a.replace(".",
"_")}function y(a,b,c){var d=p(a);Ma.push(a);Da[d]=ta[d]=!!c;ua[d]=b}function C(a,b,c){var d=p(a);Ma.push(a);Array.isArray(c)?(ta[d]=c.slice(),Da[d]=c.slice()):ta[d]=Da[d]=c;ma[d]=b}function w(){var a=Yb({cache:l}),c=a.link,d=a.global;a.id=ra++;a.batchId="0";var e=c(tb),f=a.shared={props:"a0"};Object.keys(tb).forEach(function(a){f[a]=d.def(e,".",a)});var g=a.next={},k=a.current={};Object.keys(ma).forEach(function(a){Array.isArray(ta[a])&&(g[a]=d.def(f.next,".",a),k[a]=d.def(f.current,".",a))});var ca=
a.constants={};Object.keys(Oa).forEach(function(a){ca[a]=d.def(JSON.stringify(Oa[a]))});a.invoke=function(b,d){switch(d.type){case 0:var e=["this",f.context,f.props,a.batchId];return b.def(c(d.data),".call(",e.slice(0,Math.max(d.data.length+1,4)),")");case 1:return b.def(f.props,d.data);case 2:return b.def(f.context,d.data);case 3:return b.def("this",d.data);case 4:return d.data.append(a,b),d.data.ref;case 5:return d.data.toString();case 6:return d.data.map(function(c){return a.invoke(b,c)})}};a.attribCache=
{};var N={};a.scopeAttrib=function(a){a=b.id(a);if(a in N)return N[a];var d=r.scope[a];d||(d=r.scope[a]=new ha);return N[a]=c(d)};return a}function z(a){var b=a["static"];a=a.dynamic;var c;if("profile"in b){var d=!!b.profile;c=E(function(a,b){return d});c.enable=d}else if("profile"in a){var e=a.profile;c=L(e,function(a,b){return a.invoke(b,e)})}return c}function B(a,b){var c=a["static"],d=a.dynamic;if("framebuffer"in c){var e=c.framebuffer;return e?(e=q.getFramebuffer(e),E(function(a,b){var c=a.link(e),
d=a.shared;b.set(d.framebuffer,".next",c);d=d.context;b.set(d,".framebufferWidth",c+".width");b.set(d,".framebufferHeight",c+".height");return c})):E(function(a,b){var c=a.shared;b.set(c.framebuffer,".next","null");c=c.context;b.set(c,".framebufferWidth",c+".drawingBufferWidth");b.set(c,".framebufferHeight",c+".drawingBufferHeight");return"null"})}if("framebuffer"in d){var f=d.framebuffer;return L(f,function(a,b){var c=a.invoke(b,f),d=a.shared,e=d.framebuffer,c=b.def(e,".getFramebuffer(",c,")");b.set(e,
".next",c);d=d.context;b.set(d,".framebufferWidth",c+"?"+c+".width:"+d+".drawingBufferWidth");b.set(d,".framebufferHeight",c+"?"+c+".height:"+d+".drawingBufferHeight");return c})}return null}function H(a,b,c){function d(a){if(a in e){var c=e[a];a=!0;var x=c.x|0,g=c.y|0,k,h;"width"in c?k=c.width|0:a=!1;"height"in c?h=c.height|0:a=!1;return new K(!a&&b&&b.thisDep,!a&&b&&b.contextDep,!a&&b&&b.propDep,function(a,b){var d=a.shared.context,e=k;"width"in c||(e=b.def(d,".","framebufferWidth","-",x));var f=
h;"height"in c||(f=b.def(d,".","framebufferHeight","-",g));return[x,g,e,f]})}if(a in f){var aa=f[a];a=L(aa,function(a,b){var c=a.invoke(b,aa),d=a.shared.context,e=b.def(c,".x|0"),f=b.def(c,".y|0"),N=b.def('"width" in ',c,"?",c,".width|0:","(",d,".","framebufferWidth","-",e,")"),c=b.def('"height" in ',c,"?",c,".height|0:","(",d,".","framebufferHeight","-",f,")");return[e,f,N,c]});b&&(a.thisDep=a.thisDep||b.thisDep,a.contextDep=a.contextDep||b.contextDep,a.propDep=a.propDep||b.propDep);return a}return b?
new K(b.thisDep,b.contextDep,b.propDep,function(a,b){var c=a.shared.context;return[0,0,b.def(c,".","framebufferWidth"),b.def(c,".","framebufferHeight")]}):null}var e=a["static"],f=a.dynamic;if(a=d("viewport")){var g=a;a=new K(a.thisDep,a.contextDep,a.propDep,function(a,b){var c=g.append(a,b),d=a.shared.context;b.set(d,".viewportWidth",c[2]);b.set(d,".viewportHeight",c[3]);return c})}return{viewport:a,scissor_box:d("scissor.box")}}function G(a,b){var c=a["static"];if("string"===typeof c.frag&&"string"===
typeof c.vert){if(0<Object.keys(b.dynamic).length)return null;var c=b["static"],d=Object.keys(c);if(0<d.length&&"number"===typeof c[d[0]]){for(var e=[],f=0;f<d.length;++f)e.push([c[d[f]]|0,d[f]]);return e}}return null}function D(a,c,d){function e(a){if(a in f){var c=b.id(f[a]);a=E(function(){return c});a.id=c;return a}if(a in g){var d=g[a];return L(d,function(a,b){var c=a.invoke(b,d);return b.def(a.shared.strings,".id(",c,")")})}return null}var f=a["static"],g=a.dynamic,h=e("frag"),ca=e("vert"),N=
null;la(h)&&la(ca)?(N=k.program(ca.id,h.id,null,d),a=E(function(a,b){return a.link(N)})):a=new K(h&&h.thisDep||ca&&ca.thisDep,h&&h.contextDep||ca&&ca.contextDep,h&&h.propDep||ca&&ca.propDep,function(a,b){var c=a.shared.shader,d;d=h?h.append(a,b):b.def(c,".","frag");var e;e=ca?ca.append(a,b):b.def(c,".","vert");return b.def(c+".program("+e+","+d+")")});return{frag:h,vert:ca,progVar:a,program:N}}function v(a,b){function c(a,b){if(a in d){var e=d[a]|0;b?g.offset=e:g.instances=e;return E(function(a,c){b&&
(a.OFFSET=e);return e})}if(a in f){var x=f[a];return L(x,function(a,c){var d=a.invoke(c,x);b&&(a.OFFSET=d);return d})}if(b){if(N)return E(function(a,b){return a.OFFSET=0});if(k)return new K(h.thisDep,h.contextDep,h.propDep,function(a,b){return b.def(a.shared.vao+".currentVAO?"+a.shared.vao+".currentVAO.offset:0")})}else if(k)return new K(h.thisDep,h.contextDep,h.propDep,function(a,b){return b.def(a.shared.vao+".currentVAO?"+a.shared.vao+".currentVAO.instances:-1")});return null}var d=a["static"],
f=a.dynamic,g={},k=!1,h=function(){if("vao"in d){var a=d.vao;null!==a&&null===r.getVAO(a)&&(a=r.createVAO(a));k=!0;g.vao=a;return E(function(b){var c=r.getVAO(a);return c?b.link(c):"null"})}if("vao"in f){k=!0;var b=f.vao;return L(b,function(a,c){var d=a.invoke(c,b);return c.def(a.shared.vao+".getVAO("+d+")")})}return null}(),N=!1,ia=function(){if("elements"in d){var a=d.elements;g.elements=a;if(Sa(a)){var b=g.elements=e.create(a,!0),a=e.getElements(b);N=!0}else a&&(a=e.getElements(a),N=!0);b=E(function(b,
c){if(a){var d=b.link(a);return b.ELEMENTS=d}return b.ELEMENTS=null});b.value=a;return b}if("elements"in f){N=!0;var c=f.elements;return L(c,function(a,b){var d=a.shared,e=d.isBufferArgs,d=d.elements,f=a.invoke(b,c),N=b.def("null"),e=b.def(e,"(",f,")"),f=a.cond(e).then(N,"=",d,".createStream(",f,");")["else"](N,"=",d,".getElements(",f,");");b.entry(f);b.exit(a.cond(e).then(d,".destroyStream(",N,");"));return a.ELEMENTS=N})}return k?new K(h.thisDep,h.contextDep,h.propDep,function(a,b){return b.def(a.shared.vao+
".currentVAO?"+a.shared.elements+".getElements("+a.shared.vao+".currentVAO.elements):null")}):null}(),va=c("offset",!0),l=function(){if("primitive"in d){var a=d.primitive;g.primitive=a;return E(function(b,c){return Ka[a]})}if("primitive"in f){var b=f.primitive;return L(b,function(a,c){var d=a.constants.primTypes,e=a.invoke(c,b);return c.def(d,"[",e,"]")})}return N?la(ia)?ia.value?E(function(a,b){return b.def(a.ELEMENTS,".primType")}):E(function(){return 4}):new K(ia.thisDep,ia.contextDep,ia.propDep,
function(a,b){var c=a.ELEMENTS;return b.def(c,"?",c,".primType:",4)}):k?new K(h.thisDep,h.contextDep,h.propDep,function(a,b){return b.def(a.shared.vao+".currentVAO?"+a.shared.vao+".currentVAO.primitive:4")}):null}(),n=function(){if("count"in d){var a=d.count|0;g.count=a;return E(function(){return a})}if("count"in f){var b=f.count;return L(b,function(a,c){return a.invoke(c,b)})}return N?la(ia)?ia?va?new K(va.thisDep,va.contextDep,va.propDep,function(a,b){return b.def(a.ELEMENTS,".vertCount-",a.OFFSET)}):
E(function(a,b){return b.def(a.ELEMENTS,".vertCount")}):E(function(){return-1}):new K(ia.thisDep||va.thisDep,ia.contextDep||va.contextDep,ia.propDep||va.propDep,function(a,b){var c=a.ELEMENTS;return a.OFFSET?b.def(c,"?",c,".vertCount-",a.OFFSET,":-1"):b.def(c,"?",c,".vertCount:-1")}):k?new K(h.thisDep,h.contextDep,h.propDep,function(a,b){return b.def(a.shared.vao,".currentVAO?",a.shared.vao,".currentVAO.count:-1")}):null}(),m=c("instances",!1);return{elements:ia,primitive:l,count:n,instances:m,offset:va,
vao:h,vaoActive:k,elementsActive:N,"static":g}}function ba(a,b){var c=a["static"],d=a.dynamic,e={};Ma.forEach(function(a){function b(N,g){if(a in c){var x=N(c[a]);e[f]=E(function(){return x})}else if(a in d){var F=d[a];e[f]=L(F,function(a,b){return g(a,b,a.invoke(b,F))})}}var f=p(a);switch(a){case "cull.enable":case "blend.enable":case "dither":case "stencil.enable":case "depth.enable":case "scissor.enable":case "polygonOffset.enable":case "sample.alpha":case "sample.enable":case "depth.mask":return b(function(a){return a},
function(a,b,c){return c});case "depth.func":return b(function(a){return $a[a]},function(a,b,c){return b.def(a.constants.compareFuncs,"[",c,"]")});case "depth.range":return b(function(a){return a},function(a,b,c){a=b.def("+",c,"[0]");b=b.def("+",c,"[1]");return[a,b]});case "blend.func":return b(function(a){return[Ga["srcRGB"in a?a.srcRGB:a.src],Ga["dstRGB"in a?a.dstRGB:a.dst],Ga["srcAlpha"in a?a.srcAlpha:a.src],Ga["dstAlpha"in a?a.dstAlpha:a.dst]]},function(a,b,c){function d(a,e){return b.def('"',
a,e,'" in ',c,"?",c,".",a,e,":",c,".",a)}a=a.constants.blendFuncs;var e=d("src","RGB"),f=d("dst","RGB"),e=b.def(a,"[",e,"]"),g=b.def(a,"[",d("src","Alpha"),"]"),f=b.def(a,"[",f,"]");a=b.def(a,"[",d("dst","Alpha"),"]");return[e,f,g,a]});case "blend.equation":return b(function(a){if("string"===typeof a)return[X[a],X[a]];if("object"===typeof a)return[X[a.rgb],X[a.alpha]]},function(a,b,c){var d=a.constants.blendEquations,e=b.def(),f=b.def();a=a.cond("typeof ",c,'==="string"');a.then(e,"=",f,"=",d,"[",
c,"];");a["else"](e,"=",d,"[",c,".rgb];",f,"=",d,"[",c,".alpha];");b(a);return[e,f]});case "blend.color":return b(function(a){return S(4,function(b){return+a[b]})},function(a,b,c){return S(4,function(a){return b.def("+",c,"[",a,"]")})});case "stencil.mask":return b(function(a){return a|0},function(a,b,c){return b.def(c,"|0")});case "stencil.func":return b(function(a){return[$a[a.cmp||"keep"],a.ref||0,"mask"in a?a.mask:-1]},function(a,b,c){a=b.def('"cmp" in ',c,"?",a.constants.compareFuncs,"[",c,".cmp]",
":",7680);var d=b.def(c,".ref|0");b=b.def('"mask" in ',c,"?",c,".mask|0:-1");return[a,d,b]});case "stencil.opFront":case "stencil.opBack":return b(function(b){return["stencil.opBack"===a?1029:1028,Ta[b.fail||"keep"],Ta[b.zfail||"keep"],Ta[b.zpass||"keep"]]},function(b,c,d){function e(a){return c.def('"',a,'" in ',d,"?",f,"[",d,".",a,"]:",7680)}var f=b.constants.stencilOps;return["stencil.opBack"===a?1029:1028,e("fail"),e("zfail"),e("zpass")]});case "polygonOffset.offset":return b(function(a){return[a.factor|
0,a.units|0]},function(a,b,c){a=b.def(c,".factor|0");b=b.def(c,".units|0");return[a,b]});case "cull.face":return b(function(a){var b=0;"front"===a?b=1028:"back"===a&&(b=1029);return b},function(a,b,c){return b.def(c,'==="front"?',1028,":",1029)});case "lineWidth":return b(function(a){return a},function(a,b,c){return c});case "frontFace":return b(function(a){return zb[a]},function(a,b,c){return b.def(c+'==="cw"?2304:2305')});case "colorMask":return b(function(a){return a.map(function(a){return!!a})},
function(a,b,c){return S(4,function(a){return"!!"+c+"["+a+"]"})});case "sample.coverage":return b(function(a){return["value"in a?a.value:1,!!a.invert]},function(a,b,c){a=b.def('"value" in ',c,"?+",c,".value:1");b=b.def("!!",c,".invert");return[a,b]})}});return e}function P(a,b){var c=a["static"],d=a.dynamic,e={};Object.keys(c).forEach(function(a){var b=c[a],d;if("number"===typeof b||"boolean"===typeof b)d=E(function(){return b});else if("function"===typeof b){var f=b._reglType;if("texture2d"===f||
"textureCube"===f)d=E(function(a){return a.link(b)});else if("framebuffer"===f||"framebufferCube"===f)d=E(function(a){return a.link(b.color[0])})}else qa(b)&&(d=E(function(a){return a.global.def("[",S(b.length,function(a){return b[a]}),"]")}));d.value=b;e[a]=d});Object.keys(d).forEach(function(a){var b=d[a];e[a]=L(b,function(a,c){return a.invoke(c,b)})});return e}function A(a,c){var d=a["static"],e=a.dynamic,g={};Object.keys(d).forEach(function(a){var c=d[a],e=b.id(a),x=new ha;if(Sa(c))x.state=1,
x.buffer=f.getBuffer(f.create(c,34962,!1,!0)),x.type=0;else{var F=f.getBuffer(c);if(F)x.state=1,x.buffer=F,x.type=0;else if("constant"in c){var h=c.constant;x.buffer="null";x.state=2;"number"===typeof h?x.x=h:Ca.forEach(function(a,b){b<h.length&&(x[a]=h[b])})}else{var F=Sa(c.buffer)?f.getBuffer(f.create(c.buffer,34962,!1,!0)):f.getBuffer(c.buffer),k=c.offset|0,l=c.stride|0,n=c.size|0,Fa=!!c.normalized,m=0;"type"in c&&(m=Ja[c.type]);c=c.divisor|0;x.buffer=F;x.state=1;x.size=n;x.normalized=Fa;x.type=
m||F.dtype;x.offset=k;x.stride=l;x.divisor=c}}g[a]=E(function(a,b){var c=a.attribCache;if(e in c)return c[e];var d={isStream:!1};Object.keys(x).forEach(function(a){d[a]=x[a]});x.buffer&&(d.buffer=a.link(x.buffer),d.type=d.type||d.buffer+".dtype");return c[e]=d})});Object.keys(e).forEach(function(a){var b=e[a];g[a]=L(b,function(a,c){function d(a){c(F[a],"=",e,".",a,"|0;")}var e=a.invoke(c,b),f=a.shared,g=a.constants,x=f.isBufferArgs,f=f.buffer,F={isStream:c.def(!1)},h=new ha;h.state=1;Object.keys(h).forEach(function(a){F[a]=
c.def(""+h[a])});var k=F.buffer,l=F.type;c("if(",x,"(",e,")){",F.isStream,"=true;",k,"=",f,".createStream(",34962,",",e,");",l,"=",k,".dtype;","}else{",k,"=",f,".getBuffer(",e,");","if(",k,"){",l,"=",k,".dtype;",'}else if("constant" in ',e,"){",F.state,"=",2,";","if(typeof "+e+'.constant === "number"){',F[Ca[0]],"=",e,".constant;",Ca.slice(1).map(function(a){return F[a]}).join("="),"=0;","}else{",Ca.map(function(a,b){return F[a]+"="+e+".constant.length>"+b+"?"+e+".constant["+b+"]:0;"}).join(""),"}}else{",
"if(",x,"(",e,".buffer)){",k,"=",f,".createStream(",34962,",",e,".buffer);","}else{",k,"=",f,".getBuffer(",e,".buffer);","}",l,'="type" in ',e,"?",g.glTypes,"[",e,".type]:",k,".dtype;",F.normalized,"=!!",e,".normalized;");d("size");d("offset");d("stride");d("divisor");c("}}");c.exit("if(",F.isStream,"){",f,".destroyStream(",k,");","}");return F})});return g}function J(a){var b=a["static"],c=a.dynamic,d={};Object.keys(b).forEach(function(a){var c=b[a];d[a]=E(function(a,b){return"number"===typeof c||
"boolean"===typeof c?""+c:a.link(c)})});Object.keys(c).forEach(function(a){var b=c[a];d[a]=L(b,function(a,c){return a.invoke(c,b)})});return d}function oa(a,b,d,e,f){function g(a){var b=l[a];b&&(m[a]=b)}var k=G(a,b),h=B(a,f),l=H(a,h,f),n=v(a,f),m=ba(a,f),q=D(a,f,k);g("viewport");g(p("scissor.box"));var t=0<Object.keys(m).length,h={framebuffer:h,draw:n,shader:q,state:m,dirty:t,scopeVAO:null,drawVAO:null,useVAO:!1,attributes:{}};h.profile=z(a,f);h.uniforms=P(d,f);h.drawVAO=h.scopeVAO=n.vao;if(!h.drawVAO&&
q.program&&!k&&c.angle_instanced_arrays&&n["static"].elements){var u=!0;a=q.program.attributes.map(function(a){a=b["static"][a];u=u&&!!a;return a});if(u&&0<a.length){var $b=r.getVAO(r.createVAO({attributes:a,elements:n["static"].elements}));h.drawVAO=new K(null,null,null,function(a,b){return a.link($b)});h.useVAO=!0}}k?h.useVAO=!0:h.attributes=A(b,f);h.context=J(e,f);return h}function na(a,b,c){var d=a.shared.context,e=a.scope();Object.keys(c).forEach(function(f){b.save(d,"."+f);var g=c[f].append(a,
b);Array.isArray(g)?e(d,".",f,"=[",g.join(),"];"):e(d,".",f,"=",g,";")});b(e)}function V(a,b,c,d){var e=a.shared,f=e.gl,g=e.framebuffer,h;R&&(h=b.def(e.extensions,".webgl_draw_buffers"));var k=a.constants,e=k.drawBuffer,k=k.backBuffer;a=c?c.append(a,b):b.def(g,".next");d||b("if(",a,"!==",g,".cur){");b("if(",a,"){",f,".bindFramebuffer(",36160,",",a,".framebuffer);");R&&b(h,".drawBuffersWEBGL(",e,"[",a,".colorAttachments.length]);");b("}else{",f,".bindFramebuffer(",36160,",null);");R&&b(h,".drawBuffersWEBGL(",
k,");");b("}",g,".cur=",a,";");d||b("}")}function T(a,b,c){var d=a.shared,e=d.gl,f=a.current,g=a.next,h=d.current,k=d.next,l=a.cond(h,".dirty");Ma.forEach(function(b){b=p(b);if(!(b in c.state)){var d,F;if(b in g){d=g[b];F=f[b];var n=S(ta[b].length,function(a){return l.def(d,"[",a,"]")});l(a.cond(n.map(function(a,b){return a+"!=="+F+"["+b+"]"}).join("||")).then(e,".",ma[b],"(",n,");",n.map(function(a,b){return F+"["+b+"]="+a}).join(";"),";"))}else d=l.def(k,".",b),n=a.cond(d,"!==",h,".",b),l(n),b in
ua?n(a.cond(d).then(e,".enable(",ua[b],");")["else"](e,".disable(",ua[b],");"),h,".",b,"=",d,";"):n(e,".",ma[b],"(",d,");",h,".",b,"=",d,";")}});0===Object.keys(c.state).length&&l(h,".dirty=false;");b(l)}function W(a,b,c,d){var e=a.shared,f=a.current,g=e.current,h=e.gl,k;yb(Object.keys(c)).forEach(function(e){var l=c[e];if(!d||d(l)){var n=l.append(a,b);if(ua[e]){var m=ua[e];la(l)?(k=a.link(n,{stable:!0}),b(a.cond(k).then(h,".enable(",m,");")["else"](h,".disable(",m,");")),b(g,".",e,"=",k,";")):(b(a.cond(n).then(h,
".enable(",m,");")["else"](h,".disable(",m,");")),b(g,".",e,"=",n,";"))}else if(qa(n)){var p=f[e];b(h,".",ma[e],"(",n,");",n.map(function(a,b){return p+"["+b+"]="+a}).join(";"),";")}else la(l)?(k=a.link(n,{stable:!0}),b(h,".",ma[e],"(",k,");",g,".",e,"=",k,";")):b(h,".",ma[e],"(",n,");",g,".",e,"=",n,";")}})}function Q(a,b){sa&&(a.instancing=b.def(a.shared.extensions,".angle_instanced_arrays"))}function I(a,b,c,d,e){function f(){return"undefined"===typeof performance?"Date.now()":"performance.now()"}
function h(a){t=b.def();a(t,"=",f(),";");"string"===typeof e?a(m,".count+=",e,";"):a(m,".count++;");g&&(d?(r=b.def(),a(r,"=",q,".getNumPendingQueries();")):a(q,".beginQuery(",m,");"))}function k(a){a(m,".cpuTime+=",f(),"-",t,";");g&&(d?a(q,".pushScopeStats(",r,",",q,".getNumPendingQueries(),",m,");"):a(q,".endQuery();"))}function l(a){var c=b.def(p,".profile");b(p,".profile=",a,";");b.exit(p,".profile=",c,";")}var n=a.shared,m=a.stats,p=n.current,q=n.timer;c=c.profile;var t,r;if(c){if(la(c)){c.enable?
(h(b),k(b.exit),l("true")):l("false");return}c=c.append(a,b);l(c)}else c=b.def(p,".profile");n=a.block();h(n);b("if(",c,"){",n,"}");a=a.block();k(a);b.exit("if(",c,"){",a,"}")}function M(a,b,c,d,e){function f(a){switch(a){case 35664:case 35667:case 35671:return 2;case 35665:case 35668:case 35672:return 3;case 35666:case 35669:case 35673:return 4;default:return 1}}function g(c,d,e){function f(){b("if(!",m,".buffer){",n,".enableVertexAttribArray(",l,");}");var c=e.type,g;g=e.size?b.def(e.size,"||",
d):d;b("if(",m,".type!==",c,"||",m,".size!==",g,"||",q.map(function(a){return m+"."+a+"!=="+e[a]}).join("||"),"){",n,".bindBuffer(",34962,",",p,".buffer);",n,".vertexAttribPointer(",[l,g,c,e.normalized,e.stride,e.offset],");",m,".type=",c,";",m,".size=",g,";",q.map(function(a){return m+"."+a+"="+e[a]+";"}).join(""),"}");sa&&(c=e.divisor,b("if(",m,".divisor!==",c,"){",a.instancing,".vertexAttribDivisorANGLE(",[l,c],");",m,".divisor=",c,";}"))}function k(){b("if(",m,".buffer){",n,".disableVertexAttribArray(",
l,");",m,".buffer=null;","}if(",Ca.map(function(a,b){return m+"."+a+"!=="+aa[b]}).join("||"),"){",n,".vertexAttrib4f(",l,",",aa,");",Ca.map(function(a,b){return m+"."+a+"="+aa[b]+";"}).join(""),"}")}var n=h.gl,l=b.def(c,".location"),m=b.def(h.attributes,"[",l,"]");c=e.state;var p=e.buffer,aa=[e.x,e.y,e.z,e.w],q=["buffer","normalized","offset","stride"];1===c?f():2===c?k():(b("if(",c,"===",1,"){"),f(),b("}else{"),k(),b("}"))}var h=a.shared;d.forEach(function(d){var h=d.name,k=c.attributes[h],l;if(k){if(!e(k))return;
l=k.append(a,b)}else{if(!e(Ab))return;var n=a.scopeAttrib(h);l={};Object.keys(new ha).forEach(function(a){l[a]=b.def(n,".",a)})}g(a.link(d),f(d.info.type),l)})}function U(a,c,d,e,f,g){for(var h=a.shared,k=h.gl,l,n=0;n<e.length;++n){var m=e[n],p=m.name,q=m.info.type,t=d.uniforms[p],m=a.link(m)+".location",r;if(t){if(!f(t))continue;if(la(t)){p=t.value;if(35678===q||35680===q)q=a.link(p._texture||p.color[0]._texture),c(k,".uniform1i(",m,",",q+".bind());"),c.exit(q,".unbind();");else if(35674===q||35675===
q||35676===q)p=a.global.def("new Float32Array(["+Array.prototype.slice.call(p)+"])"),t=2,35675===q?t=3:35676===q&&(t=4),c(k,".uniformMatrix",t,"fv(",m,",false,",p,");");else{switch(q){case 5126:l="1f";break;case 35664:l="2f";break;case 35665:l="3f";break;case 35666:l="4f";break;case 35670:l="1i";break;case 5124:l="1i";break;case 35671:l="2i";break;case 35667:l="2i";break;case 35672:l="3i";break;case 35668:l="3i";break;case 35673:l="4i";break;case 35669:l="4i"}c(k,".uniform",l,"(",m,",",qa(p)?Array.prototype.slice.call(p):
p,");")}continue}else r=t.append(a,c)}else{if(!f(Ab))continue;r=c.def(h.uniforms,"[",b.id(p),"]")}35678===q?c("if(",r,"&&",r,'._reglType==="framebuffer"){',r,"=",r,".color[0];","}"):35680===q&&c("if(",r,"&&",r,'._reglType==="framebufferCube"){',r,"=",r,".color[0];","}");p=1;switch(q){case 35678:case 35680:q=c.def(r,"._texture");c(k,".uniform1i(",m,",",q,".bind());");c.exit(q,".unbind();");continue;case 5124:case 35670:l="1i";break;case 35667:case 35671:l="2i";p=2;break;case 35668:case 35672:l="3i";
p=3;break;case 35669:case 35673:l="4i";p=4;break;case 5126:l="1f";break;case 35664:l="2f";p=2;break;case 35665:l="3f";p=3;break;case 35666:l="4f";p=4;break;case 35674:l="Matrix2fv";break;case 35675:l="Matrix3fv";break;case 35676:l="Matrix4fv"}if("M"===l.charAt(0)){c(k,".uniform",l,"(",m,",");var m=Math.pow(q-35674+2,2),u=a.global.def("new Float32Array(",m,")");Array.isArray(r)?c("false,(",S(m,function(a){return u+"["+a+"]="+r[a]}),",",u,")"):c("false,(Array.isArray(",r,")||",r," instanceof Float32Array)?",
r,":(",S(m,function(a){return u+"["+a+"]="+r+"["+a+"]"}),",",u,")");c(");")}else{if(1<p){for(var q=[],v=[],t=0;t<p;++t)Array.isArray(r)?v.push(r[t]):v.push(c.def(r+"["+t+"]")),g&&q.push(c.def());g&&c("if(!",a.batchId,"||",q.map(function(a,b){return a+"!=="+v[b]}).join("||"),"){",q.map(function(a,b){return a+"="+v[b]+";"}).join(""));c(k,".uniform",l,"(",m,",",v.join(","),");")}else g&&(q=c.def(),c("if(!",a.batchId,"||",q,"!==",r,"){",q,"=",r,";")),c(k,".uniform",l,"(",m,",",r,");");g&&c("}")}}}function Y(a,
b,c,d){function e(f){var g=n[f];return g?g.contextDep&&d.contextDynamic||g.propDep?g.append(a,c):g.append(a,b):b.def(l,".",f)}function f(){function a(){c(u,".drawElementsInstancedANGLE(",[p,t,v,q+"<<(("+v+"-5121)>>1)",r],");")}function b(){c(u,".drawArraysInstancedANGLE(",[p,q,t,r],");")}m&&"null"!==m?y?a():(c("if(",m,"){"),a(),c("}else{"),b(),c("}")):b()}function g(){function a(){c(k+".drawElements("+[p,t,v,q+"<<(("+v+"-5121)>>1)"]+");")}function b(){c(k+".drawArrays("+[p,q,t]+");")}m&&"null"!==
m?y?a():(c("if(",m,"){"),a(),c("}else{"),b(),c("}")):b()}var h=a.shared,k=h.gl,l=h.draw,n=d.draw,m=function(){var e=n.elements,f=b;if(e){if(e.contextDep&&d.contextDynamic||e.propDep)f=c;e=e.append(a,f);n.elementsActive&&f("if("+e+")"+k+".bindBuffer(34963,"+e+".buffer.buffer);")}else e=f.def(),f(e,"=",l,".","elements",";","if(",e,"){",k,".bindBuffer(",34963,",",e,".buffer.buffer);}","else if(",h.vao,".currentVAO){",e,"=",a.shared.elements+".getElements("+h.vao,".currentVAO.elements);",ja?"":"if("+
e+")"+k+".bindBuffer(34963,"+e+".buffer.buffer);","}");return e}(),p=e("primitive"),q=e("offset"),t=function(){var e=n.count,f=b;if(e){if(e.contextDep&&d.contextDynamic||e.propDep)f=c;e=e.append(a,f)}else e=f.def(l,".","count");return e}();if("number"===typeof t){if(0===t)return}else c("if(",t,"){"),c.exit("}");var r,u;sa&&(r=e("instances"),u=a.instancing);var v=m+".type",y=n.elements&&la(n.elements)&&!n.vaoActive;sa&&("number"!==typeof r||0<=r)?"string"===typeof r?(c("if(",r,">0){"),f(),c("}else if(",
r,"<0){"),g(),c("}")):f():g()}function xa(a,b,c,d,e){b=w();e=b.proc("body",e);sa&&(b.instancing=e.def(b.shared.extensions,".angle_instanced_arrays"));a(b,e,c,d);return b.compile().body}function da(a,b,c,d){Q(a,b);c.useVAO?c.drawVAO?b(a.shared.vao,".setVAO(",c.drawVAO.append(a,b),");"):b(a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);"):(b(a.shared.vao,".setVAO(null);"),M(a,b,c,d.attributes,function(){return!0}));U(a,b,c,d.uniforms,function(){return!0},!1);Y(a,b,b,c)}function pa(a,b){var c=a.proc("draw",
1);Q(a,c);na(a,c,b.context);V(a,c,b.framebuffer);T(a,c,b);W(a,c,b.state);I(a,c,b,!1,!0);var d=b.shader.progVar.append(a,c);c(a.shared.gl,".useProgram(",d,".program);");if(b.shader.program)da(a,c,b,b.shader.program);else{c(a.shared.vao,".setVAO(null);");var e=a.global.def("{}"),f=c.def(d,".id"),g=c.def(e,"[",f,"]");c(a.cond(g).then(g,".call(this,a0);")["else"](g,"=",e,"[",f,"]=",a.link(function(c){return xa(da,a,b,c,1)}),"(",d,");",g,".call(this,a0);"))}0<Object.keys(b.state).length&&c(a.shared.current,
".dirty=true;");a.shared.vao&&c(a.shared.vao,".setVAO(null);")}function ga(a,b,c,d){function e(){return!0}a.batchId="a1";Q(a,b);M(a,b,c,d.attributes,e);U(a,b,c,d.uniforms,e,!1);Y(a,b,b,c)}function Ea(a,b,c,d){function e(a){return a.contextDep&&g||a.propDep}function f(a){return!e(a)}Q(a,b);var g=c.contextDep,h=b.def(),k=b.def();a.shared.props=k;a.batchId=h;var l=a.scope(),n=a.scope();b(l.entry,"for(",h,"=0;",h,"<","a1",";++",h,"){",k,"=","a0","[",h,"];",n,"}",l.exit);c.needsContext&&na(a,n,c.context);
c.needsFramebuffer&&V(a,n,c.framebuffer);W(a,n,c.state,e);c.profile&&e(c.profile)&&I(a,n,c,!1,!0);d?(c.useVAO?c.drawVAO?e(c.drawVAO)?n(a.shared.vao,".setVAO(",c.drawVAO.append(a,n),");"):l(a.shared.vao,".setVAO(",c.drawVAO.append(a,l),");"):l(a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);"):(l(a.shared.vao,".setVAO(null);"),M(a,l,c,d.attributes,f),M(a,n,c,d.attributes,e)),U(a,l,c,d.uniforms,f,!1),U(a,n,c,d.uniforms,e,!0),Y(a,l,n,c)):(b=a.global.def("{}"),d=c.shader.progVar.append(a,n),k=n.def(d,
".id"),l=n.def(b,"[",k,"]"),n(a.shared.gl,".useProgram(",d,".program);","if(!",l,"){",l,"=",b,"[",k,"]=",a.link(function(b){return xa(ga,a,c,b,2)}),"(",d,");}",l,".call(this,a0[",h,"],",h,");"))}function Qa(a,b){function c(a){return a.contextDep&&e||a.propDep}var d=a.proc("batch",2);a.batchId="0";Q(a,d);var e=!1,f=!0;Object.keys(b.context).forEach(function(a){e=e||b.context[a].propDep});e||(na(a,d,b.context),f=!1);var g=b.framebuffer,h=!1;g?(g.propDep?e=h=!0:g.contextDep&&e&&(h=!0),h||V(a,d,g)):V(a,
d,null);b.state.viewport&&b.state.viewport.propDep&&(e=!0);T(a,d,b);W(a,d,b.state,function(a){return!c(a)});b.profile&&c(b.profile)||I(a,d,b,!1,"a1");b.contextDep=e;b.needsContext=f;b.needsFramebuffer=h;f=b.shader.progVar;if(f.contextDep&&e||f.propDep)Ea(a,d,b,null);else if(f=f.append(a,d),d(a.shared.gl,".useProgram(",f,".program);"),b.shader.program)Ea(a,d,b,b.shader.program);else{d(a.shared.vao,".setVAO(null);");var g=a.global.def("{}"),h=d.def(f,".id"),k=d.def(g,"[",h,"]");d(a.cond(k).then(k,".call(this,a0,a1);")["else"](k,
"=",g,"[",h,"]=",a.link(function(c){return xa(Ea,a,b,c,2)}),"(",f,");",k,".call(this,a0,a1);"))}0<Object.keys(b.state).length&&d(a.shared.current,".dirty=true;");a.shared.vao&&d(a.shared.vao,".setVAO(null);")}function ea(a,c){function d(b){var g=c.shader[b];g&&(g=g.append(a,e),isNaN(g)?e.set(f.shader,"."+b,g):e.set(f.shader,"."+b,a.link(g,{stable:!0})))}var e=a.proc("scope",3);a.batchId="a2";var f=a.shared,g=f.current;na(a,e,c.context);c.framebuffer&&c.framebuffer.append(a,e);yb(Object.keys(c.state)).forEach(function(b){var d=
c.state[b],g=d.append(a,e);qa(g)?g.forEach(function(c,d){isNaN(c)?e.set(a.next[b],"["+d+"]",c):e.set(a.next[b],"["+d+"]",a.link(c,{stable:!0}))}):la(d)?e.set(f.next,"."+b,a.link(g,{stable:!0})):e.set(f.next,"."+b,g)});I(a,e,c,!0,!0);["elements","offset","count","instances","primitive"].forEach(function(b){var d=c.draw[b];d&&(d=d.append(a,e),isNaN(d)?e.set(f.draw,"."+b,d):e.set(f.draw,"."+b,a.link(d),{stable:!0}))});Object.keys(c.uniforms).forEach(function(d){var g=c.uniforms[d].append(a,e);Array.isArray(g)&&
(g="["+g.map(function(b){return isNaN(b)?b:a.link(b,{stable:!0})})+"]");e.set(f.uniforms,"["+a.link(b.id(d),{stable:!0})+"]",g)});Object.keys(c.attributes).forEach(function(b){var d=c.attributes[b].append(a,e),f=a.scopeAttrib(b);Object.keys(new ha).forEach(function(a){e.set(f,"."+a,d[a])})});if(c.scopeVAO){var h=c.scopeVAO.append(a,e);isNaN(h)?e.set(f.vao,".targetVAO",h):e.set(f.vao,".targetVAO",a.link(h,{stable:!0}))}d("vert");d("frag");0<Object.keys(c.state).length&&(e(g,".dirty=true;"),e.exit(g,
".dirty=true;"));e("a1(",a.shared.context,",a0,",a.batchId,");")}function fa(a){if("object"===typeof a&&!qa(a)){for(var b=Object.keys(a),c=0;c<b.length;++c)if(Z.isDynamic(a[b[c]]))return!0;return!1}}function ka(a,b,c){function d(a,b){g.forEach(function(c){var d=e[c];Z.isDynamic(d)&&(d=a.invoke(b,d),b(n,".",c,"=",d,";"))})}var e=b["static"][c];if(e&&fa(e)){var f=a.global,g=Object.keys(e),h=!1,k=!1,l=!1,n=a.global.def("{}");g.forEach(function(b){var c=e[b];if(Z.isDynamic(c))"function"===typeof c&&(c=
e[b]=Z.unbox(c)),b=L(c,null),h=h||b.thisDep,l=l||b.propDep,k=k||b.contextDep;else{f(n,".",b,"=");switch(typeof c){case "number":f(c);break;case "string":f('"',c,'"');break;case "object":Array.isArray(c)&&f("[",c.join(),"]");break;default:f(a.link(c))}f(";")}});b.dynamic[c]=new Z.DynamicVariable(4,{thisDep:h,contextDep:k,propDep:l,ref:n,append:d});delete b["static"][c]}}var ha=r.Record,X={add:32774,subtract:32778,"reverse subtract":32779};c.ext_blend_minmax&&(X.min=32775,X.max=32776);var sa=c.angle_instanced_arrays,
R=c.webgl_draw_buffers,ja=c.oes_vertex_array_object,ta={dirty:!0,profile:h.profile},Da={},Ma=[],ua={},ma={};y("dither",3024);y("blend.enable",3042);C("blend.color","blendColor",[0,0,0,0]);C("blend.equation","blendEquationSeparate",[32774,32774]);C("blend.func","blendFuncSeparate",[1,0,1,0]);y("depth.enable",2929,!0);C("depth.func","depthFunc",513);C("depth.range","depthRange",[0,1]);C("depth.mask","depthMask",!0);C("colorMask","colorMask",[!0,!0,!0,!0]);y("cull.enable",2884);C("cull.face","cullFace",
1029);C("frontFace","frontFace",2305);C("lineWidth","lineWidth",1);y("polygonOffset.enable",32823);C("polygonOffset.offset","polygonOffset",[0,0]);y("sample.alpha",32926);y("sample.enable",32928);C("sample.coverage","sampleCoverage",[1,!1]);y("stencil.enable",2960);C("stencil.mask","stencilMask",-1);C("stencil.func","stencilFunc",[519,0,-1]);C("stencil.opFront","stencilOpSeparate",[1028,7680,7680,7680]);C("stencil.opBack","stencilOpSeparate",[1029,7680,7680,7680]);y("scissor.enable",3089);C("scissor.box",
"scissor",[0,0,a.drawingBufferWidth,a.drawingBufferHeight]);C("viewport","viewport",[0,0,a.drawingBufferWidth,a.drawingBufferHeight]);var tb={gl:a,context:t,strings:b,next:Da,current:ta,draw:n,elements:e,buffer:f,shader:k,attributes:r.state,vao:r,uniforms:u,framebuffer:q,extensions:c,timer:g,isBufferArgs:Sa},Oa={primTypes:Ka,compareFuncs:$a,blendFuncs:Ga,blendEquations:X,stencilOps:Ta,glTypes:Ja,orientationType:zb};R&&(Oa.backBuffer=[1029],Oa.drawBuffer=S(d.maxDrawbuffers,function(a){return 0===a?
[0]:S(a,function(a){return 36064+a})}));var ra=0;return{next:Da,current:ta,procs:function(){var a=w(),b=a.proc("poll"),e=a.proc("refresh"),f=a.block();b(f);e(f);var g=a.shared,h=g.gl,k=g.next,l=g.current;f(l,".dirty=false;");V(a,b);V(a,e,null,!0);var n;sa&&(n=a.link(sa));c.oes_vertex_array_object&&e(a.link(c.oes_vertex_array_object),".bindVertexArrayOES(null);");var g=e.def(g.attributes),m=e.def(0),p=a.cond(m,".buffer");p.then(h,".enableVertexAttribArray(i);",h,".bindBuffer(",34962,",",m,".buffer.buffer);",
h,".vertexAttribPointer(i,",m,".size,",m,".type,",m,".normalized,",m,".stride,",m,".offset);")["else"](h,".disableVertexAttribArray(i);",h,".vertexAttrib4f(i,",m,".x,",m,".y,",m,".z,",m,".w);",m,".buffer=null;");var q=a.link(d.maxAttributes,{stable:!0});e("for(var i=0;i<",q,";++i){",m,"=",g,"[i];",p,"}");sa&&e("for(var i=0;i<",q,";++i){",n,".vertexAttribDivisorANGLE(i,",g,"[i].divisor);","}");e(a.shared.vao,".currentVAO=null;",a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);");Object.keys(ua).forEach(function(c){var d=
ua[c],g=f.def(k,".",c),n=a.block();n("if(",g,"){",h,".enable(",d,")}else{",h,".disable(",d,")}",l,".",c,"=",g,";");e(n);b("if(",g,"!==",l,".",c,"){",n,"}")});Object.keys(ma).forEach(function(c){var d=ma[c],g=ta[c],n,m,p=a.block();p(h,".",d,"(");qa(g)?(d=g.length,n=a.global.def(k,".",c),m=a.global.def(l,".",c),p(S(d,function(a){return n+"["+a+"]"}),");",S(d,function(a){return m+"["+a+"]="+n+"["+a+"];"}).join("")),b("if(",S(d,function(a){return n+"["+a+"]!=="+m+"["+a+"]"}).join("||"),"){",p,"}")):(n=
f.def(k,".",c),m=f.def(l,".",c),p(n,");",l,".",c,"=",n,";"),b("if(",n,"!==",m,"){",p,"}"));e(p)});return a.compile()}(),compile:function(a,b,c,d,e){var f=w();f.stats=f.link(e);Object.keys(b["static"]).forEach(function(a){ka(f,b,a)});ac.forEach(function(b){ka(f,a,b)});var g=oa(a,b,c,d,f);g.shader.program&&(g.shader.program.attributes.sort(function(a,b){return a.name<b.name?-1:1}),g.shader.program.uniforms.sort(function(a,b){return a.name<b.name?-1:1}));pa(f,g);ea(f,g);Qa(f,g);return O(f.compile(),
{destroy:function(){g.shader.program.destroy()}})}}}function Bb(a,b){for(var c=0;c<a.length;++c)if(a[c]===b)return c;return-1}var O=function(a,b){for(var c=Object.keys(b),d=0;d<c.length;++d)a[c[d]]=b[c[d]];return a},Db=0,Z={DynamicVariable:da,define:function(a,b){return new da(a,bb(b+""))},isDynamic:function(a){return"function"===typeof a&&!a._reglType||a instanceof da},unbox:cb,accessor:bb},ab={next:"function"===typeof requestAnimationFrame?function(a){return requestAnimationFrame(a)}:function(a){return setTimeout(a,
16)},cancel:"function"===typeof cancelAnimationFrame?function(a){return cancelAnimationFrame(a)}:clearTimeout},Cb="undefined"!==typeof performance&&performance.now?function(){return performance.now()}:function(){return+new Date},B=gb();B.zero=gb();var bc=function(a,b){var c=1;b.ext_texture_filter_anisotropic&&(c=a.getParameter(34047));var d=1,f=1;b.webgl_draw_buffers&&(d=a.getParameter(34852),f=a.getParameter(36063));var e=!!b.oes_texture_float;if(e){e=a.createTexture();a.bindTexture(3553,e);a.texImage2D(3553,
0,6408,1,1,0,6408,5126,null);var m=a.createFramebuffer();a.bindFramebuffer(36160,m);a.framebufferTexture2D(36160,36064,3553,e,0);a.bindTexture(3553,null);if(36053!==a.checkFramebufferStatus(36160))e=!1;else{a.viewport(0,0,1,1);a.clearColor(1,0,0,1);a.clear(16384);var q=B.allocType(5126,4);a.readPixels(0,0,1,1,6408,5126,q);a.getError()?e=!1:(a.deleteFramebuffer(m),a.deleteTexture(e),e=1===q[0]);B.freeType(q)}}q=!0;"undefined"!==typeof navigator&&(/MSIE/.test(navigator.userAgent)||/Trident\//.test(navigator.appVersion)||
/Edge/.test(navigator.userAgent))||(q=a.createTexture(),m=B.allocType(5121,36),a.activeTexture(33984),a.bindTexture(34067,q),a.texImage2D(34069,0,6408,3,3,0,6408,5121,m),B.freeType(m),a.bindTexture(34067,null),a.deleteTexture(q),q=!a.getError());return{colorBits:[a.getParameter(3410),a.getParameter(3411),a.getParameter(3412),a.getParameter(3413)],depthBits:a.getParameter(3414),stencilBits:a.getParameter(3415),subpixelBits:a.getParameter(3408),extensions:Object.keys(b).filter(function(a){return!!b[a]}),
maxAnisotropic:c,maxDrawbuffers:d,maxColorAttachments:f,pointSizeDims:a.getParameter(33901),lineWidthDims:a.getParameter(33902),maxViewportDims:a.getParameter(3386),maxCombinedTextureUnits:a.getParameter(35661),maxCubeMapSize:a.getParameter(34076),maxRenderbufferSize:a.getParameter(34024),maxTextureUnits:a.getParameter(34930),maxTextureSize:a.getParameter(3379),maxAttributes:a.getParameter(34921),maxVertexUniforms:a.getParameter(36347),maxVertexTextureUnits:a.getParameter(35660),maxVaryingVectors:a.getParameter(36348),
maxFragmentUniforms:a.getParameter(36349),glsl:a.getParameter(35724),renderer:a.getParameter(7937),vendor:a.getParameter(7936),version:a.getParameter(7938),readFloat:e,npotTextureCube:q}},P=function(a){return a instanceof Uint8Array||a instanceof Uint16Array||a instanceof Uint32Array||a instanceof Int8Array||a instanceof Int16Array||a instanceof Int32Array||a instanceof Float32Array||a instanceof Float64Array||a instanceof Uint8ClampedArray},T=function(a){return Object.keys(a).map(function(b){return a[b]})},
Pa={shape:function(a){for(var b=[];a.length;a=a[0])b.push(a.length);return b},flatten:function(a,b,c,d){var f=1;if(b.length)for(var e=0;e<b.length;++e)f*=b[e];else f=0;c=d||B.allocType(c,f);switch(b.length){case 0:break;case 1:d=b[0];for(b=0;b<d;++b)c[b]=a[b];break;case 2:d=b[0];b=b[1];for(e=f=0;e<d;++e)for(var m=a[e],q=0;q<b;++q)c[f++]=m[q];break;case 3:hb(a,b[0],b[1],b[2],c,0);break;default:ib(a,b,0,c,0)}return c}},Ia={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,
"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121},Ja={int8:5120,int16:5122,int32:5124,uint8:5121,uint16:5123,uint32:5125,"float":5126,float32:5126},mb={dynamic:35048,stream:35040,"static":35044},Ua=Pa.flatten,lb=Pa.shape,ja=[];ja[5120]=1;ja[5122]=2;ja[5124]=4;ja[5121]=1;ja[5123]=2;ja[5125]=4;ja[5126]=4;var Ka={points:0,point:0,lines:1,line:1,triangles:4,
triangle:4,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6},pb=new Float32Array(1),Lb=new Uint32Array(pb.buffer),Pb=[9984,9986,9985,9987],Na=[0,6409,6410,6407,6408],U={};U[6409]=U[6406]=U[6402]=1;U[34041]=U[6410]=2;U[6407]=U[35904]=3;U[6408]=U[35906]=4;var Wa=ra("HTMLCanvasElement"),Xa=ra("OffscreenCanvas"),ub=ra("CanvasRenderingContext2D"),vb=ra("ImageBitmap"),wb=ra("HTMLImageElement"),xb=ra("HTMLVideoElement"),Mb=Object.keys(Ia).concat([Wa,Xa,ub,vb,wb,xb]),ya=[];ya[5121]=1;ya[5126]=
4;ya[36193]=2;ya[5123]=2;ya[5125]=4;var G=[];G[32854]=2;G[32855]=2;G[36194]=2;G[34041]=4;G[33776]=.5;G[33777]=.5;G[33778]=1;G[33779]=1;G[35986]=.5;G[35987]=1;G[34798]=1;G[35840]=.5;G[35841]=.25;G[35842]=.5;G[35843]=.25;G[36196]=.5;var M=[];M[32854]=2;M[32855]=2;M[36194]=2;M[33189]=2;M[36168]=1;M[34041]=4;M[35907]=4;M[34836]=16;M[34842]=8;M[34843]=6;var cc=function(a,b,c,d,f){function e(a){this.id=r++;this.refCount=1;this.renderbuffer=a;this.format=32854;this.height=this.width=0;f.profile&&(this.stats=
{size:0})}function m(b){var c=b.renderbuffer;a.bindRenderbuffer(36161,null);a.deleteRenderbuffer(c);b.renderbuffer=null;b.refCount=0;delete k[b.id];d.renderbufferCount--}var q={rgba4:32854,rgb565:36194,"rgb5 a1":32855,depth:33189,stencil:36168,"depth stencil":34041};b.ext_srgb&&(q.srgba=35907);b.ext_color_buffer_half_float&&(q.rgba16f=34842,q.rgb16f=34843);b.webgl_color_buffer_float&&(q.rgba32f=34836);var u=[];Object.keys(q).forEach(function(a){u[q[a]]=a});var r=0,k={};e.prototype.decRef=function(){0>=
--this.refCount&&m(this)};f.profile&&(d.getTotalRenderbufferSize=function(){var a=0;Object.keys(k).forEach(function(b){a+=k[b].stats.size});return a});return{create:function(b,c){function g(b,c){var d=0,e=0,k=32854;"object"===typeof b&&b?("shape"in b?(e=b.shape,d=e[0]|0,e=e[1]|0):("radius"in b&&(d=e=b.radius|0),"width"in b&&(d=b.width|0),"height"in b&&(e=b.height|0)),"format"in b&&(k=q[b.format])):"number"===typeof b?(d=b|0,e="number"===typeof c?c|0:d):b||(d=e=1);if(d!==l.width||e!==l.height||k!==
l.format)return g.width=l.width=d,g.height=l.height=e,l.format=k,a.bindRenderbuffer(36161,l.renderbuffer),a.renderbufferStorage(36161,k,d,e),f.profile&&(l.stats.size=M[l.format]*l.width*l.height),g.format=u[l.format],g}var l=new e(a.createRenderbuffer());k[l.id]=l;d.renderbufferCount++;g(b,c);g.resize=function(b,c){var d=b|0,e=c|0||d;if(d===l.width&&e===l.height)return g;g.width=l.width=d;g.height=l.height=e;a.bindRenderbuffer(36161,l.renderbuffer);a.renderbufferStorage(36161,l.format,d,e);f.profile&&
(l.stats.size=M[l.format]*l.width*l.height);return g};g._reglType="renderbuffer";g._renderbuffer=l;f.profile&&(g.stats=l.stats);g.destroy=function(){l.decRef()};return g},clear:function(){T(k).forEach(m)},restore:function(){T(k).forEach(function(b){b.renderbuffer=a.createRenderbuffer();a.bindRenderbuffer(36161,b.renderbuffer);a.renderbufferStorage(36161,b.format,b.width,b.height)});a.bindRenderbuffer(36161,null)}}},Ya=[];Ya[6408]=4;Ya[6407]=3;var Ra=[];Ra[5121]=1;Ra[5126]=4;Ra[36193]=2;var Vb=[1116352408,
1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,
275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998],Ca=["x","y","z","w"],ac="blend.func blend.equation stencil.func stencil.opFront stencil.opBack sample.coverage viewport scissor.box polygonOffset.offset".split(" "),Ga={0:0,1:1,zero:0,one:1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,
"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776},$a={never:512,less:513,"<":513,equal:514,"=":514,"==":514,"===":514,lequal:515,"<=":515,greater:516,">":516,notequal:517,"!=":517,"!==":517,gequal:518,">=":518,always:519},Ta={0:0,zero:0,keep:7680,replace:7681,increment:7682,decrement:7683,"increment wrap":34055,"decrement wrap":34056,invert:5386},zb={cw:2304,ccw:2305},
Ab=new K(!1,!1,!1,function(){}),dc=function(a,b){function c(){this.endQueryIndex=this.startQueryIndex=-1;this.sum=0;this.stats=null}function d(a,b,d){var e=m.pop()||new c;e.startQueryIndex=a;e.endQueryIndex=b;e.sum=0;e.stats=d;q.push(e)}if(!b.ext_disjoint_timer_query)return null;var f=[],e=[],m=[],q=[],u=[],r=[];return{beginQuery:function(a){var c=f.pop()||b.ext_disjoint_timer_query.createQueryEXT();b.ext_disjoint_timer_query.beginQueryEXT(35007,c);e.push(c);d(e.length-1,e.length,a)},endQuery:function(){b.ext_disjoint_timer_query.endQueryEXT(35007)},
pushScopeStats:d,update:function(){var a,c;a=e.length;if(0!==a){r.length=Math.max(r.length,a+1);u.length=Math.max(u.length,a+1);u[0]=0;var d=r[0]=0;for(c=a=0;c<e.length;++c){var g=e[c];b.ext_disjoint_timer_query.getQueryObjectEXT(g,34919)?(d+=b.ext_disjoint_timer_query.getQueryObjectEXT(g,34918),f.push(g)):e[a++]=g;u[c+1]=d;r[c+1]=a}e.length=a;for(c=a=0;c<q.length;++c){var d=q[c],l=d.startQueryIndex,g=d.endQueryIndex;d.sum+=u[g]-u[l];l=r[l];g=r[g];g===l?(d.stats.gpuTime+=d.sum/1E6,m.push(d)):(d.startQueryIndex=
l,d.endQueryIndex=g,q[a++]=d)}q.length=a}},getNumPendingQueries:function(){return e.length},clear:function(){f.push.apply(f,e);for(var a=0;a<f.length;a++)b.ext_disjoint_timer_query.deleteQueryEXT(f[a]);e.length=0;f.length=0},restore:function(){e.length=0;f.length=0}}};return function(a){function b(){if(0===I.length)z&&z.update(),R=null;else{R=ab.next(b);k();for(var a=I.length-1;0<=a;--a){var c=I[a];c&&c(D,null,0)}g.flush();z&&z.update()}}function c(){!R&&0<I.length&&(R=ab.next(b))}function d(){R&&
(ab.cancel(b),R=null)}function f(a){a.preventDefault();d();S.forEach(function(a){a()})}function e(a){g.getError();h.restore();J.restore();G.restore();L.restore();P.restore();V.restore();A.restore();z&&z.restore();M.procs.refresh();c();T.forEach(function(a){a()})}function m(a){function b(a,c){var d={},e={};Object.keys(a).forEach(function(b){var f=a[b];if(Z.isDynamic(f))e[b]=Z.unbox(f,b);else{if(c&&Array.isArray(f))for(var g=0;g<f.length;++g)if(Z.isDynamic(f[g])){e[b]=Z.unbox(f,b);return}d[b]=f}});
return{dynamic:e,"static":d}}function c(a){for(;n.length<a;)n.push(null);return n}var d=b(a.context||{},!0),e=b(a.uniforms||{},!0),f=b(a.attributes||{},!1);a=b(function(a){function b(a){if(a in c){var d=c[a];delete c[a];Object.keys(d).forEach(function(b){c[a+"."+b]=d[b]})}}var c=O({},a);delete c.uniforms;delete c.attributes;delete c.context;delete c.vao;"stencil"in c&&c.stencil.op&&(c.stencil.opBack=c.stencil.opFront=c.stencil.op,delete c.stencil.op);b("blend");b("depth");b("cull");b("stencil");b("polygonOffset");
b("scissor");b("sample");"vao"in a&&(c.vao=a.vao);return c}(a),!1);var g={gpuTime:0,cpuTime:0,count:0},h=M.compile(a,f,e,d,g),k=h.draw,l=h.batch,m=h.scope,n=[];return O(function(a,b){var d;if("function"===typeof a)return m.call(this,null,a,0);if("function"===typeof b)if("number"===typeof a)for(d=0;d<a;++d)m.call(this,null,b,d);else if(Array.isArray(a))for(d=0;d<a.length;++d)m.call(this,a[d],b,d);else return m.call(this,a,b,0);else if("number"===typeof a){if(0<a)return l.call(this,c(a|0),a|0)}else if(Array.isArray(a)){if(a.length)return l.call(this,
a,a.length)}else return k.call(this,a)},{stats:g,destroy:function(){h.destroy()}})}function q(a,b){var c=0;M.procs.poll();var d=b.color;d&&(g.clearColor(+d[0]||0,+d[1]||0,+d[2]||0,+d[3]||0),c|=16384);"depth"in b&&(g.clearDepth(+b.depth),c|=256);"stencil"in b&&(g.clearStencil(b.stencil|0),c|=1024);g.clear(c)}function u(a){I.push(a);c();return{cancel:function(){function b(){var a=Bb(I,b);I[a]=I[I.length-1];--I.length;0>=I.length&&d()}var c=Bb(I,a);I[c]=b}}}function r(){var a=W.viewport,b=W.scissor_box;
a[0]=a[1]=b[0]=b[1]=0;D.viewportWidth=D.framebufferWidth=D.drawingBufferWidth=a[2]=b[2]=g.drawingBufferWidth;D.viewportHeight=D.framebufferHeight=D.drawingBufferHeight=a[3]=b[3]=g.drawingBufferHeight}function k(){D.tick+=1;D.time=t();r();M.procs.poll()}function n(){L.refresh();r();M.procs.refresh();z&&z.update()}function t(){return(Cb()-E)/1E3}a=Hb(a);if(!a)return null;var g=a.gl,l=g.getContextAttributes();g.isContextLost();var h=Ib(g,a);if(!h)return null;var p=Eb(),y={vaoCount:0,bufferCount:0,elementsCount:0,
framebufferCount:0,shaderCount:0,textureCount:0,cubeCount:0,renderbufferCount:0,maxTextureUnits:0},C=a.cachedCode||{},w=h.extensions,z=dc(g,w),E=Cb(),B=g.drawingBufferWidth,H=g.drawingBufferHeight,D={tick:0,time:0,viewportWidth:B,viewportHeight:H,framebufferWidth:B,framebufferHeight:H,drawingBufferWidth:B,drawingBufferHeight:H,pixelRatio:a.pixelRatio},B={elements:null,primitive:4,count:-1,offset:0,instances:-1},v=bc(g,w),G=Jb(g,y,a,function(a){return A.destroyBuffer(a)}),K=Kb(g,w,G,y),A=Rb(g,w,v,
y,G,K,B),J=Sb(g,p,y,a),L=Nb(g,w,v,function(){M.procs.poll()},D,y,a),P=cc(g,w,v,y,a),V=Qb(g,w,v,L,P,y),M=Zb(g,p,w,v,G,K,L,V,{},A,J,B,D,z,C,a),p=Tb(g,V,M.procs.poll,D,l,w,v),W=M.next,Q=g.canvas,I=[],S=[],T=[],U=[a.onDestroy],R=null;Q&&(Q.addEventListener("webglcontextlost",f,!1),Q.addEventListener("webglcontextrestored",e,!1));var Y=V.setFBO=m({framebuffer:Z.define.call(null,1,"framebuffer")});n();l=O(m,{clear:function(a){if("framebuffer"in a)if(a.framebuffer&&"framebufferCube"===a.framebuffer_reglType)for(var b=
0;6>b;++b)Y(O({framebuffer:a.framebuffer.faces[b]},a),q);else Y(a,q);else q(null,a)},prop:Z.define.bind(null,1),context:Z.define.bind(null,2),"this":Z.define.bind(null,3),draw:m({}),buffer:function(a){return G.create(a,34962,!1,!1)},elements:function(a){return K.create(a,!1)},texture:L.create2D,cube:L.createCube,renderbuffer:P.create,framebuffer:V.create,framebufferCube:V.createCube,vao:A.createVAO,attributes:l,frame:u,on:function(a,b){var c;switch(a){case "frame":return u(b);case "lost":c=S;break;
case "restore":c=T;break;case "destroy":c=U}c.push(b);return{cancel:function(){for(var a=0;a<c.length;++a)if(c[a]===b){c[a]=c[c.length-1];c.pop();break}}}},limits:v,hasExtension:function(a){return 0<=v.extensions.indexOf(a.toLowerCase())},read:p,destroy:function(){I.length=0;d();Q&&(Q.removeEventListener("webglcontextlost",f),Q.removeEventListener("webglcontextrestored",e));J.clear();V.clear();P.clear();A.clear();L.clear();K.clear();G.clear();z&&z.clear();U.forEach(function(a){a()})},_gl:g,_refresh:n,
poll:function(){k();z&&z.update()},now:t,stats:y,getCachedCode:function(){return C},preloadCachedCode:function(a){Object.entries(a).forEach(function(a){C[a[0]]=a[1]})}});a.onDone(null,l);return l}});
