{"name": "styled-jsx", "version": "5.1.1", "license": "MIT", "repository": "vercel/styled-jsx", "description": "Full CSS support for JSX without compromises", "files": ["dist", "lib", "global.d.ts", "index.d.ts", "index.js", "babel.js", "babel-test.js", "style.js", "style.d.ts", "macro.js", "macro.d.ts", "css.js", "css.d.ts", "webpack.js", "license.md"], "typings": "./index.d.ts", "scripts": {"build-babel": "bunchee src/babel.js -f cjs -e babel-plugin-macros --runtime node -o dist/babel/index.js", "build": "rm -rf dist && rm -rf out && yarn build-webpack && yarn build-index && yarn build-babel", "build-webpack": "bunchee src/webpack.js -f cjs --runtime node -o dist/webpack/index.js", "build-index": "bunchee src/index.js -f cjs --runtime node -o dist/index/index.js", "test": "ava", "lint": "eslint ./src", "format": "prettier --write \"./{src,test}/**/*.{js,css}\"", "prepublishOnly": "yarn build && yarn test && yarn lint --quiet"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "ava": {"require": ["@babel/register"]}, "eslintConfig": {"env": {"node": true, "browser": true, "es6": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": 11, "sourceType": "module"}, "rules": {"no-empty": 0, "capitalized-comments": 0, "valid-jsdoc": 0, "prefer-destructuring": 0, "padding-line-between-statements": 0}}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/core": "7.12.3", "@babel/plugin-proposal-object-rest-spread": "7.12.1", "@babel/plugin-syntax-jsx": "7.14.5", "@babel/plugin-transform-arrow-functions": "7.12.1", "@babel/plugin-transform-modules-commonjs": "7.12.1", "@babel/plugin-transform-runtime": "7.12.1", "@babel/preset-env": "7.12.1", "@babel/preset-react": "7.12.5", "@babel/register": "7.12.1", "@babel/runtime": "7.12.5", "@babel/types": "7.15.0", "ava": "4.3.1", "babel-plugin-macros": "2.8.0", "bunchee": "2.1.5", "convert-source-map": "1.7.0", "eslint": "7.32.0", "eslint-config-prettier": "4.0.0", "husky": "4.3.0", "loader-utils": "1.4.1", "prettier": "1.16.4", "pretty-quick": "3.1.0", "react": "17.0.1", "react-dom": "17.0.1", "semantic-release": "17.2.2", "source-map": "0.7.3", "string-hash": "1.1.3", "stylis": "3.5.4", "stylis-rule-sheet": "0.0.10"}, "peerDependencies": {"react": ">= 16.8.0 || 17.x.x || ^18.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "babel-plugin-macros": {"optional": true}}, "release": {"branches": ["main", "alpha", "beta"]}, "engines": {"node": ">= 12.0.0"}, "keywords": ["babel-plugin-macros", "vercel", "zeit", "css-in-js", "css"], "dependencies": {"client-only": "0.0.1"}}