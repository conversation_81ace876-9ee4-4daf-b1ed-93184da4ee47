
'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/lib/auth';
import { TradingMode } from '@prisma/client';
import { LogIn, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';

interface LoginFormProps {
  onSuccess: () => void;
}

export function LoginForm({ onSuccess }: LoginFormProps) {
  const [email, setEmail] = useState('');
  const [mode, setMode] = useState<TradingMode>(TradingMode.DEMO);
  const { login, isLoading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Veuillez saisir votre email');
      return;
    }

    try {
      await login(email, mode);
      toast.success('Connexion réussie');
      onSuccess();
    } catch (error) {
      toast.error('Erreur de connexion');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
      <Card className="w-full max-w-md mx-4 bg-slate-900/50 backdrop-blur border-slate-700">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 rounded-full bg-blue-600/20">
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">BingX Trading</CardTitle>
          <CardDescription className="text-slate-400">
            Plateforme de trading simple et sécurisée
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-slate-200">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mode" className="text-slate-200">Mode de trading</Label>
              <Select value={mode} onValueChange={(value) => setMode(value as TradingMode)}>
                <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-600">
                  <SelectItem value="DEMO" className="text-green-400">
                    Mode Démo (1000$ USDT virtuel)
                  </SelectItem>
                  <SelectItem value="LIVE" className="text-red-400">
                    Mode Réel (Vraies transactions)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              disabled={isLoading}
            >
              <LogIn className="w-4 h-4 mr-2" />
              {isLoading ? 'Connexion...' : 'Se connecter'}
            </Button>

            <div className="text-center text-sm text-slate-400 space-y-2">
              <p>🟢 <strong>Mode Démo:</strong> Trading virtuel avec 1000$ USDT</p>
              <p>🔴 <strong>Mode Réel:</strong> Trading avec vos vraies clés API</p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
