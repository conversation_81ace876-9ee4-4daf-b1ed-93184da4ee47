{"version": 3, "sources": ["../../src/mutation.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#defaultOptions = config.defaultOptions\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    const executeMutation = () => {\n      this.#retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject(new Error('No mutationFn found'))\n          }\n          return this.options.mutationFn(variables)\n        },\n        onFail: (failureCount, error) => {\n          this.#dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.#dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.#dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.#retryer.promise\n    }\n\n    const restored = this.state.status === 'pending'\n\n    try {\n      if (!restored) {\n        this.#dispatch({ type: 'pending', variables })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAA8B;AAC9B,uBAA0B;AAC1B,qBAAwC;AA8EjC,IAAM,WAAN,cAKG,2BAAU;AAAA,EAUlB,YAAY,QAA6D;AACvE,UAAM;AAEN,SAAK,aAAa,OAAO;AACzB,SAAK,kBAAkB,OAAO;AAC9B,SAAK,iBAAiB,OAAO;AAC7B,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ,OAAO,SAAS,gBAAgB;AAE7C,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,WAAW;AAAA,EAClB;AAAA,EAhBA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAeA,WACE,SACM;AACN,SAAK,UAAU,EAAE,GAAG,KAAK,iBAAiB,GAAG,QAAQ;AAErD,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACvC;AAAA,EAEA,IAAI,OAAiC;AACnC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,YAAY,UAAsD;AAChE,QAAI,CAAC,KAAK,WAAW,SAAS,QAAQ,GAAG;AACvC,WAAK,WAAW,KAAK,QAAQ;AAG7B,WAAK,eAAe;AAEpB,WAAK,eAAe,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,eAAe,UAAsD;AACnE,SAAK,aAAa,KAAK,WAAW,OAAO,CAAC,MAAM,MAAM,QAAQ;AAE9D,SAAK,WAAW;AAEhB,SAAK,eAAe,OAAO;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEU,iBAAiB;AACzB,QAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,UAAI,KAAK,MAAM,WAAW,WAAW;AACnC,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,eAAe,OAAO,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAA6B;AAC3B,WACE,KAAK,UAAU,SAAS;AAAA,IAExB,KAAK,QAAQ,KAAK,MAAM,SAAU;AAAA,EAEtC;AAAA,EAEA,MAAM,QAAQ,WAAuC;AACnD,UAAM,kBAAkB,MAAM;AAC5B,WAAK,eAAW,8BAAc;AAAA,QAC5B,IAAI,MAAM;AACR,cAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,mBAAO,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC;AAAA,UACxD;AACA,iBAAO,KAAK,QAAQ,WAAW,SAAS;AAAA,QAC1C;AAAA,QACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,eAAK,UAAU,EAAE,MAAM,UAAU,cAAc,MAAM,CAAC;AAAA,QACxD;AAAA,QACA,SAAS,MAAM;AACb,eAAK,UAAU,EAAE,MAAM,QAAQ,CAAC;AAAA,QAClC;AAAA,QACA,YAAY,MAAM;AAChB,eAAK,UAAU,EAAE,MAAM,WAAW,CAAC;AAAA,QACrC;AAAA,QACA,OAAO,KAAK,QAAQ,SAAS;AAAA,QAC7B,YAAY,KAAK,QAAQ;AAAA,QACzB,aAAa,KAAK,QAAQ;AAAA,MAC5B,CAAC;AAED,aAAO,KAAK,SAAS;AAAA,IACvB;AAEA,UAAM,WAAW,KAAK,MAAM,WAAW;AAEvC,QAAI;AACF,UAAI,CAAC,UAAU;AACb,aAAK,UAAU,EAAE,MAAM,WAAW,UAAU,CAAC;AAE7C,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AACA,cAAM,UAAU,MAAM,KAAK,QAAQ,WAAW,SAAS;AACvD,YAAI,YAAY,KAAK,MAAM,SAAS;AAClC,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,OAAO,MAAM,gBAAgB;AAGnC,YAAM,KAAK,eAAe,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,MACF;AAEA,YAAM,KAAK,QAAQ,YAAY,MAAM,WAAW,KAAK,MAAM,OAAO;AAGlE,YAAM,KAAK,eAAe,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX;AAAA,MACF;AAEA,YAAM,KAAK,QAAQ,YAAY,MAAM,MAAM,WAAW,KAAK,MAAM,OAAO;AAExE,WAAK,UAAU,EAAE,MAAM,WAAW,KAAK,CAAC;AACxC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI;AAEF,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA,QACF;AAEA,cAAM,KAAK,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,QACb;AAGA,cAAM,KAAK,eAAe,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX,KAAK,MAAM;AAAA,UACX;AAAA,QACF;AAEA,cAAM,KAAK,QAAQ;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,QACb;AACA,cAAM;AAAA,MACR,UAAE;AACA,aAAK,UAAU,EAAE,MAAM,SAAS,MAAuB,CAAC;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU,QAA2D;AACnE,UAAM,UAAU,CACd,UACuD;AACvD,cAAQ,OAAO,MAAM;AAAA,QACnB,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,cAAc,OAAO;AAAA,YACrB,eAAe,OAAO;AAAA,UACxB;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,SAAS,OAAO;AAAA,YAChB,MAAM;AAAA,YACN,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU,KAAC,yBAAS,KAAK,QAAQ,WAAW;AAAA,YAC5C,QAAQ;AAAA,YACR,WAAW,OAAO;AAAA,YAClB,aAAa,KAAK,IAAI;AAAA,UACxB;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM,OAAO;AAAA,YACb,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,YACN,OAAO,OAAO;AAAA,YACd,cAAc,MAAM,eAAe;AAAA,YACnC,eAAe,OAAO;AAAA,YACtB,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,MACJ;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,uCAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,CAAC,aAAa;AACpC,iBAAS,iBAAiB,MAAM;AAAA,MAClC,CAAC;AACD,WAAK,eAAe,OAAO;AAAA,QACzB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAEO,SAAS,kBAKwC;AACtD,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AACF;", "names": []}