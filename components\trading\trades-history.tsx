
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { History, RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';
import { TradeStatus, TradeSide, TradeType } from '@prisma/client';
import { toast } from 'sonner';

interface Trade {
  id: string;
  symbol: string;
  side: TradeSide;
  type: TradeType;
  quantity: number;
  price?: number;
  executedPrice?: number;
  status: TradeStatus;
  profit?: number;
  createdAt: string;
}

export function TradesHistory() {
  const { user } = useAuth();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchTrades = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/trading/trades?userId=${user.id}&mode=${user.mode}&tradingType=${user.tradingType || 'SPOT'}`);
      if (!response.ok) throw new Error('Erreur récupération historique');
      
      const data = await response.json();
      setTrades(data || []);
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement de l\'historique');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrades();
    
    // Auto-refresh toutes les 30 secondes
    const intervalId: NodeJS.Timeout = setInterval(() => {
      fetchTrades();
    }, 30000);
    return () => clearInterval(intervalId);
  }, [user, user?.tradingType]); // Recharger aussi quand le tradingType change

  const formatPrice = (price?: number | null) => {
    // Vérification et conversion robuste en nombre
    const numPrice = typeof price === 'number' && !isNaN(price) ? price : null;
    if (!numPrice || numPrice === 0) return '--';
    
    if (numPrice > 1) return numPrice.toFixed(2);
    if (numPrice > 0.1) return numPrice.toFixed(4);
    return numPrice.toFixed(6);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '--';
    }
  };

  const getStatusBadge = (status: TradeStatus) => {
    switch (status) {
      case TradeStatus.FILLED:
        return <Badge className="bg-green-600/20 text-green-400">Exécuté</Badge>;
      case TradeStatus.PENDING:
        return <Badge className="bg-yellow-600/20 text-yellow-400">En attente</Badge>;
      case TradeStatus.CANCELLED:
        return <Badge className="bg-gray-600/20 text-gray-400">Annulé</Badge>;
      case TradeStatus.FAILED:
        return <Badge className="bg-red-600/20 text-red-400">Échoué</Badge>;
      default:
        return <Badge className="bg-gray-600/20 text-gray-400">Inconnu</Badge>;
    }
  };

  const getSideIcon = (side: TradeSide) => {
    return side === TradeSide.BUY ? (
      <TrendingUp className="w-4 h-4 text-green-400" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-400" />
    );
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-white flex items-center">
            <History className="w-5 h-5 mr-2" />
            Historique des trades
          </CardTitle>
          <CardDescription className="text-slate-400">
            {user?.mode === 'DEMO' ? '🟢 Trades démo' : '🔴 Trades réels'} - {trades?.length || 0} transaction(s)
          </CardDescription>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={fetchTrades}
          disabled={loading}
          className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
        >
          <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {trades?.length > 0 ? (
            trades.map((trade, index) => (
              <div 
                key={trade?.id || `trade-${index}`}
                className="p-3 bg-slate-800/30 rounded-lg border border-slate-700/50"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getSideIcon(trade?.side)}
                    <span className={`font-medium ${
                      trade?.side === TradeSide.BUY ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {trade?.side === TradeSide.BUY ? 'ACHAT' : 'VENTE'}
                    </span>
                    <span className="text-white font-medium">{trade?.symbol}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(trade?.status)}
                    <Badge variant="outline" className="border-slate-600 text-slate-300">
                      {trade?.type === TradeType.MARKET ? 'Marché' : 'Limite'}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                  <div>
                    <div className="text-slate-400">Quantité</div>
                    <div className="text-white font-medium">{trade?.quantity || 0}</div>
                  </div>
                  <div>
                    <div className="text-slate-400">Prix</div>
                    <div className="text-white font-medium">
                      ${formatPrice(trade?.executedPrice || trade?.price)}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400">Total</div>
                    <div className="text-white font-medium">
                      ${(() => {
                        const qty = parseFloat(String(trade?.quantity || 0));
                        const price = parseFloat(String(trade?.executedPrice || trade?.price || 0));
                        const total = !isNaN(qty) && !isNaN(price) ? qty * price : 0;
                        return formatPrice(total);
                      })()}
                    </div>
                  </div>
                  <div>
                    <div className="text-slate-400">Date</div>
                    <div className="text-white">{formatDate(trade?.createdAt || '')}</div>
                  </div>
                </div>

                {trade?.profit && trade.profit !== 0 && (
                  <div className="mt-2 pt-2 border-t border-slate-700">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-400">P&L:</span>
                      <span className={`font-medium ${
                        (trade.profit || 0) > 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {(trade.profit || 0) > 0 ? '+' : ''}${formatPrice(trade.profit)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-center text-slate-400 py-8">
              {loading ? 'Chargement...' : 'Aucun trade dans l\'historique'}
              {!loading && user?.mode === 'DEMO' && (
                <div className="mt-2 text-sm">
                  Commencez par passer votre premier ordre démo !
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
