# 🚀 Plateforme de Trading BingX

Une plateforme de trading moderne et complète intégrée à l'API BingX, développée avec Next.js 14, TypeScript et Prisma. Cette application offre une expérience de trading professionnelle avec support des modes démo et réel.

## ✨ Fonctionnalités Principales

### 🎯 Types de Trading Supportés
- **Trading Spot** - Achat et vente au comptant sans levier
- **Futures Perpétuels** - Contrats à terme sans échéance avec levier jusqu'à 125x
- **Futures Standards** - Contrats à terme avec date d'expiration
- **Copy Trading** - Copie automatique de traders experts
- **Trading Bot** - Bots automatisés et stratégies 24/7

### 🔄 Modes de Trading
- **Mode Démo** 🟢 - Environnement d'entraînement avec solde virtuel (1000 USDT)
- **Mode Réel** 🔴 - Trading en direct avec l'API BingX

### 📊 Interface de Trading
- **Graphiques en temps réel** - Visualisation des prix avec TradingView
- **Carnet d'ordres** - Affichage des ordres d'achat et de vente
- **Historique des trades** - Suivi complet des transactions
- **Gestion des positions** - Monitoring des positions ouvertes
- **Calcul P&L** - Profit et perte en temps réel

## 🛠️ Technologies Utilisées

### Frontend
- **Next.js 14** - Framework React avec App Router
- **TypeScript** - Typage statique pour une meilleure robustesse
- **Tailwind CSS** - Framework CSS utilitaire
- **Radix UI** - Composants UI accessibles et personnalisables
- **Framer Motion** - Animations fluides
- **React Hook Form** - Gestion des formulaires
- **Zustand** - Gestion d'état légère

### Backend
- **Next.js API Routes** - API serverless
- **Prisma** - ORM moderne pour PostgreSQL
- **NextAuth.js** - Authentification sécurisée
- **PostgreSQL** - Base de données relationnelle

### Intégrations
- **API BingX** - Intégration complète pour le trading
- **Chart.js / Recharts** - Graphiques et visualisations
- **React Query** - Gestion du cache et des requêtes

## 🚀 Installation et Configuration

### Prérequis
- Node.js 18+ 
- PostgreSQL
- Compte BingX avec clés API (pour le mode réel)

### Installation

1. **Cloner le repository**
```bash
git clone <votre-repo>
cd app
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration de l'environnement**
Créer un fichier `.env.local` :
```env
# Base de données
DATABASE_URL="postgresql://username:password@localhost:5432/trading_db"

# NextAuth
NEXTAUTH_SECRET="votre-secret-nextauth"
NEXTAUTH_URL="http://localhost:3000"

# API BingX (optionnel pour mode démo)
BINGX_API_KEY="votre-api-key"
BINGX_SECRET_KEY="votre-secret-key"
```

4. **Configuration de la base de données**
```bash
# Générer le client Prisma
npx prisma generate

# Appliquer les migrations
npx prisma db push

# Peupler la base (optionnel)
npm run seed
```

5. **Lancer l'application**
```bash
npm run dev
```

L'application sera accessible sur `http://localhost:3000`

## 📁 Structure du Projet

```
app/
├── app/                    # App Router Next.js
│   ├── api/               # Routes API
│   │   ├── auth/          # Authentification
│   │   ├── bingx/         # Intégration BingX
│   │   └── trading/       # Logique de trading
│   ├── globals.css        # Styles globaux
│   ├── layout.tsx         # Layout principal
│   └── page.tsx           # Page d'accueil
├── components/            # Composants React
│   ├── auth/             # Composants d'authentification
│   ├── layout/           # Composants de mise en page
│   ├── trading/          # Composants de trading
│   └── ui/               # Composants UI réutilisables
├── lib/                  # Utilitaires et services
│   ├── auth.ts           # Logique d'authentification
│   ├── bingx-api.ts      # Client API BingX
│   ├── demo-trading.ts   # Service de trading démo
│   └── db.ts             # Configuration Prisma
├── prisma/               # Schéma et migrations
└── types/                # Définitions TypeScript
```

## 🔐 Sécurité

- **Authentification sécurisée** avec NextAuth.js
- **Chiffrement des clés API** BingX
- **Validation des données** avec Zod
- **Protection CSRF** intégrée
- **Gestion sécurisée des sessions**

## 📈 Fonctionnalités de Trading

### Ordres Supportés
- **Market Orders** - Exécution immédiate au prix du marché
- **Limit Orders** - Exécution à un prix spécifique
- **Stop Loss** - Protection contre les pertes
- **Take Profit** - Prise de bénéfices automatique

### Gestion des Risques
- **Calcul automatique des coûts** estimés
- **Vérification des fonds** disponibles
- **Alertes de risque** pour les positions importantes
- **Mode démo** pour l'apprentissage sans risque

## 🧪 Tests et Développement

```bash
# Lancer en mode développement
npm run dev

# Build de production
npm run build

# Lancer en production
npm start

# Linting
npm run lint
```

## 📊 Base de Données

Le schéma Prisma inclut :
- **Users** - Gestion des utilisateurs
- **DemoBalance** - Soldes virtuels pour le mode démo
- **Trades** - Historique des transactions
- **Positions** - Positions ouvertes

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -m 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrir une Pull Request

## ⚠️ Avertissements

- **Le trading de crypto-monnaies est risqué** - Ne tradez que ce que vous pouvez vous permettre de perdre
- **Utilisez le mode démo** pour vous familiariser avec la plateforme
- **Vérifiez toujours** vos ordres avant de les placer en mode réel
- **Gardez vos clés API sécurisées** et ne les partagez jamais

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou problème :
- Ouvrir une issue sur GitHub
- Consulter la documentation BingX API
- Vérifier les logs de l'application

---

**Développé avec ❤️ pour la communauté crypto**
