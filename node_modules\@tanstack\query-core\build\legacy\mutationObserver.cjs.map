{"version": 3, "sources": ["../../src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n    this.#currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(\n            action.data,\n            this.#currentResult.variables!,\n            this.#currentResult.context!,\n          )\n          this.#mutateOptions.onSettled?.(\n            action.data,\n            null,\n            this.#currentResult.variables!,\n            this.#currentResult.context,\n          )\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(\n            action.error,\n            this.#currentResult.variables!,\n            this.#currentResult.context,\n          )\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            this.#currentResult.variables!,\n            this.#currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAgC;AAChC,2BAA8B;AAC9B,0BAA6B;AAC7B,mBAAoC;AAHpC;AAqBO,IAAM,mBAAN,cAKG,iCAER;AAAA,EASA,YACE,QACA,SACA;AACA,UAAM;AAwER;AAgBA;AAlGA;AACA,uCACE;AACF;AACA;AAQE,uBAAK,SAAU;AACf,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY;AACjB,0BAAK,gCAAL;AAAA,EACF;AAAA,EAEU,cAAoB;AAC5B,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AAAA,EAEA,WACE,SACA;AAxDJ;AAyDI,UAAM,cAAc,KAAK;AACzB,SAAK,UAAU,mBAAK,SAAQ,uBAAuB,OAAO;AAC1D,QAAI,KAAC,kCAAoB,aAAa,KAAK,OAAO,GAAG;AACnD,yBAAK,SAAQ,iBAAiB,EAAE,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,UAAU,mBAAK;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,6BAAK,sBAAL,mBAAuB,WAAW,KAAK;AAAA,EACzC;AAAA,EAEU,gBAAsB;AArElC;AAsEI,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,+BAAK,sBAAL,mBAAuB,eAAe;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,iBAAiB,QAA2D;AAC1E,0BAAK,gCAAL;AAEA,0BAAK,oBAAL,WAAa;AAAA,EACf;AAAA,EAEA,mBAKE;AACA,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,QAAc;AACZ,uBAAK,kBAAmB;AACxB,0BAAK,gCAAL;AACA,0BAAK,oBAAL;AAAA,EACF;AAAA,EAEA,OACE,WACA,SACgB;AAnGpB;AAoGI,uBAAK,gBAAiB;AAEtB,6BAAK,sBAAL,mBAAuB,eAAe;AAEtC,uBAAK,kBAAmB,mBAAK,SAC1B,iBAAiB,EACjB,MAAM,mBAAK,UAAS,KAAK,OAAO;AAEnC,uBAAK,kBAAiB,YAAY,IAAI;AAEtC,WAAO,mBAAK,kBAAiB,QAAQ,SAAS;AAAA,EAChD;AAuDF;AAvIE;AACA;AAEA;AACA;AA8EA;AAAA,kBAAa,WAAS;AAjHxB;AAkHI,QAAM,UACJ,wBAAK,sBAAL,mBAAuB,cACvB,iCAAqD;AAEvD,qBAAK,gBAAiB;AAAA,IACpB,GAAG;AAAA,IACH,WAAW,MAAM,WAAW;AAAA,IAC5B,WAAW,MAAM,WAAW;AAAA,IAC5B,SAAS,MAAM,WAAW;AAAA,IAC1B,QAAQ,MAAM,WAAW;AAAA,IACzB,QAAQ,KAAK;AAAA,IACb,OAAO,KAAK;AAAA,EACd;AACF;AAEA;AAAA,YAAO,SAAC,QAA4D;AAClE,qCAAc,MAAM,MAAM;AAlI9B;AAoIM,QAAI,mBAAK,mBAAkB,KAAK,aAAa,GAAG;AAC9C,WAAI,iCAAQ,UAAS,WAAW;AAC9B,uCAAK,iBAAe,cAApB;AAAA;AAAA,UACE,OAAO;AAAA,UACP,mBAAK,gBAAe;AAAA,UACpB,mBAAK,gBAAe;AAAA;AAEtB,uCAAK,iBAAe,cAApB;AAAA;AAAA,UACE,OAAO;AAAA,UACP;AAAA,UACA,mBAAK,gBAAe;AAAA,UACpB,mBAAK,gBAAe;AAAA;AAAA,MAExB,YAAW,iCAAQ,UAAS,SAAS;AACnC,uCAAK,iBAAe,YAApB;AAAA;AAAA,UACE,OAAO;AAAA,UACP,mBAAK,gBAAe;AAAA,UACpB,mBAAK,gBAAe;AAAA;AAEtB,uCAAK,iBAAe,cAApB;AAAA;AAAA,UACE;AAAA,UACA,OAAO;AAAA,UACP,mBAAK,gBAAe;AAAA,UACpB,mBAAK,gBAAe;AAAA;AAAA,MAExB;AAAA,IACF;AAGA,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,mBAAK,eAAc;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH;", "names": []}