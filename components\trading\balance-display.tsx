
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { Wallet, RefreshCw, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';

interface Balance {
  asset: string;
  free: string;
  locked: string;
}

export function BalanceDisplay() {
  const { user } = useAuth();
  const [balances, setBalances] = useState<Balance[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchBalance = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const tradingType = (user as any).tradingType || 'SPOT';
      const response = await fetch(`/api/bingx/balance?userId=${user.id}&mode=${user.mode}&tradingType=${tradingType}`);
      if (!response.ok) throw new Error('Erreur récupération solde');
      
      const data = await response.json();
      setBalances(data || []);
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement du solde');
    } finally {
      setLoading(false);
    }
  };

  const resetDemoBalance = async () => {
    if (!user || user.mode !== 'DEMO') return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/trading/reset-demo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });
      
      if (!response.ok) throw new Error('Erreur réinitialisation');
      
      toast.success('Solde démo réinitialisé à 1000 USDT');
      fetchBalance();
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors de la réinitialisation');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
    
    // Auto-refresh toutes les 30 secondes
    const intervalId: NodeJS.Timeout = setInterval(() => {
      fetchBalance();
    }, 30000);
    return () => clearInterval(intervalId);
  }, [user]);

  const formatAmount = (amount: string) => {
    const num = parseFloat(amount || '0');
    if (isNaN(num) || num === 0) return '0.00';
    if (num < 0.01) return num.toFixed(6);
    return num.toFixed(2);
  };

  const totalBalance = balances?.reduce?.((total, balance) => {
    // Pour les futures perpétuels, utiliser equity si disponible
    if ((balance as any).equity) {
      const equity = parseFloat((balance as any).equity || '0');
      return total + (isNaN(equity) ? 0 : equity);
    }
    
    // Pour les contrats standards, utiliser crossWalletBalance + crossUnPnl
    if ((balance as any).crossWalletBalance !== undefined) {
      const crossWallet = parseFloat((balance as any).crossWalletBalance || '0');
      const crossUnPnl = parseFloat((balance as any).crossUnPnl || '0');
      return total + (isNaN(crossWallet) ? 0 : crossWallet) + (isNaN(crossUnPnl) ? 0 : crossUnPnl);
    }
    
    // Pour les autres modes, utiliser free + locked
    const free = parseFloat(balance?.free || '0');
    const locked = parseFloat(balance?.locked || '0');
    const validFree = isNaN(free) ? 0 : free;
    const validLocked = isNaN(locked) ? 0 : locked;
    return total + validFree + validLocked;
  }, 0) || 0;

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-white flex items-center">
            <Wallet className="w-5 h-5 mr-2" />
            Portfolio
          </CardTitle>
          <CardDescription className="text-slate-400">
            {user?.mode === 'DEMO' ? '🟢 Solde virtuel' : '🔴 Solde réel'}
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          {user?.mode === 'DEMO' && (
            <Button
              size="sm"
              variant="outline"
              onClick={resetDemoBalance}
              disabled={loading}
              className="bg-orange-600/20 border-orange-600 text-orange-400 hover:bg-orange-600/30"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Reset
            </Button>
          )}
          <Button
            size="sm"
            variant="outline"
            onClick={fetchBalance}
            disabled={loading}
            className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
          >
            <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Solde total */}
          <div className="bg-slate-800/50 p-4 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                ${formatAmount(totalBalance.toString())}
              </div>
              <div className="text-sm text-slate-400">Solde total équivalent USDT</div>
            </div>
          </div>

          {/* Détail des balances */}
          <div className="space-y-2">
            {balances?.length > 0 ? (
              balances
                ?.filter?.(balance => {
                  // Pour les futures perpétuels, vérifier equity
                  if ((balance as any).equity) {
                    return parseFloat((balance as any).equity || '0') !== 0;
                  }
                  
                  // Pour les contrats standards, vérifier crossWalletBalance
                  if ((balance as any).crossWalletBalance !== undefined) {
                    const crossWallet = parseFloat((balance as any).crossWalletBalance || '0');
                    const crossUnPnl = parseFloat((balance as any).crossUnPnl || '0');
                    return crossWallet !== 0 || crossUnPnl !== 0;
                  }
                  
                  // Pour les autres modes, vérifier free + locked
                  return parseFloat(balance?.free || '0') > 0 || parseFloat(balance?.locked || '0') > 0;
                })
                ?.map?.((balance, index) => {
                  const free = parseFloat(balance?.free || '0');
                  const locked = parseFloat(balance?.locked || '0');
                  const equity = parseFloat((balance as any).equity || '0');
                  const unrealizedProfit = parseFloat((balance as any).unrealizedProfit || '0');
                  
                  // Pour les contrats standards
                  const crossWallet = parseFloat((balance as any).crossWalletBalance || '0');
                  const crossUnPnl = parseFloat((balance as any).crossUnPnl || '0');
                  const availableBalance = parseFloat((balance as any).availableBalance || '0');
                  
                  // Calculer le total selon le type
                  let total = 0;
                  let displayValue = 0;
                  
                  if (equity > 0) {
                    // Futures perpétuels
                    total = equity;
                    displayValue = equity;
                  } else if (crossWallet !== 0 || crossUnPnl !== 0) {
                    // Contrats standards
                    total = crossWallet + crossUnPnl;
                    displayValue = availableBalance || crossWallet;
                  } else {
                    // Spot
                    total = free + locked;
                    displayValue = free;
                  }
                  
                  if (total === 0 && equity === 0 && crossWallet === 0) return null;
                  
                  return (
                    <div 
                      key={balance?.asset || `balance-${index}`} 
                      className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-xs font-bold text-white">
                            {balance?.asset?.slice?.(0, 2) || 'XX'}
                          </span>
                        </div>
                        <div>
                          <div className="text-white font-medium">{balance?.asset || 'Unknown'}</div>
                          <div className="text-xs text-slate-400">
                            {equity > 0 ? (
                              <>
                                Equity: ${formatAmount(equity.toString())}
                                {unrealizedProfit !== 0 && (
                                  <span className={`ml-2 ${unrealizedProfit > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                    PnL: {unrealizedProfit > 0 ? '+' : ''}${formatAmount(unrealizedProfit.toString())}
                                  </span>
                                )}
                              </>
                            ) : crossWallet !== 0 || crossUnPnl !== 0 ? (
                              <>
                                Wallet: ${formatAmount(crossWallet.toString())}
                                {crossUnPnl !== 0 && (
                                  <span className={`ml-2 ${crossUnPnl > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                    PnL: {crossUnPnl > 0 ? '+' : ''}${formatAmount(crossUnPnl.toString())}
                                  </span>
                                )}
                              </>
                            ) : (
                              locked > 0 && `Bloqué: ${formatAmount(locked.toString())}`
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">
                          {formatAmount(displayValue.toString())}
                        </div>
                        <div className="text-xs text-slate-400">
                          {equity > 0 ? 'Total Equity' : 
                           crossWallet !== 0 || crossUnPnl !== 0 ? 'Disponible' : 
                           'Disponible'}
                        </div>
                      </div>
                    </div>
                  );
                })
            ) : (
              <div className="text-center text-slate-400 py-8">
                {loading ? 'Chargement...' : 'Aucun solde disponible'}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
