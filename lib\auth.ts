
'use client';

import { create } from 'zustand';
import { TradingMode } from '@prisma/client';

interface AuthState {
  user: {
    id: string;
    email: string;
    name?: string;
    mode: TradingMode;
    tradingType?: string;
  } | null;
  isLoading: boolean;
  login: (email: string, mode: TradingMode) => Promise<void>;
  logout: () => void;
  switchMode: (mode: TradingMode) => Promise<void>;
  updateTradingType: (tradingType: string) => void;
}

export const useAuth = create<AuthState>((set, get) => ({
  user: null,
  isLoading: false,

  login: async (email: string, mode: TradingMode) => {
    console.log('Tentative de connexion:', { email, mode });
    set({ isLoading: true });
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, mode }),
      });

      console.log('Réponse login:', response.status);
      
      if (!response.ok) {
        const errorData = await response.text();
        console.error('Erreur réponse:', errorData);
        throw new Error('Erreur de connexion');
      }

      const userData = await response.json();
      console.log('Données utilisateur reçues:', userData);
      
      set({ user: userData, isLoading: false });
      
      // Stocker dans localStorage pour la persistance
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  logout: () => {
    set({ user: null });
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user');
    }
  },

  switchMode: async (mode: TradingMode) => {
    const { user } = get();
    if (!user) return;

    try {
      const response = await fetch('/api/auth/switch-mode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id, mode }),
      });

      if (!response.ok) {
        throw new Error('Erreur lors du changement de mode');
      }

      const updatedUser = { ...user, mode };
      set({ user: updatedUser });
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }
    } catch (error) {
      console.error('Erreur lors du changement de mode:', error);
      throw error;
    }
  },

  updateTradingType: (tradingType: string) => {
    const { user } = get();
    if (!user) return;

    const updatedUser = { ...user, tradingType };
    set({ user: updatedUser });
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  }
}));

// Hook pour initialiser l'auth depuis le localStorage
export const useAuthInit = () => {
  const { user } = useAuth();

  if (typeof window !== 'undefined' && !user) {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        useAuth.setState({ user: userData });
      } catch (error) {
        console.error('Erreur lors du chargement des données utilisateur:', error);
        localStorage.removeItem('user');
      }
    }
  }

  return user;
};
