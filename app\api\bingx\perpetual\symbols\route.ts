

import { NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET() {
  try {
    console.log('Récupération symboles futures perpétuels...');
    
    const response = await bingxApi.getPerpetualTickers();
    console.log('Response perpétuels:', JSON.stringify(response, null, 2).slice(0, 300));

    // Traiter la réponse selon la structure des données BingX
    const symbols = response?.data || [];
    
    // Filtrer et formater les symboles perpétuels
    const perpetualSymbols = symbols
      .filter((symbol: any) => symbol?.symbol?.includes('USDT'))
      .map((symbol: any) => ({
        symbol: symbol.symbol,
        price: parseFloat(symbol.lastPrice || '0'),
        change: parseFloat(symbol.priceChangePercent || '0'),
        volume: parseFloat(symbol.volume || '0'),
        high: parseFloat(symbol.highPrice || '0'),
        low: parseFloat(symbol.lowPrice || '0'),
        leverage: symbol.maxLeverage || '125'
      }))
      .sort((a: any, b: any) => b.volume - a.volume)
      .slice(0, 50);

    console.log(`Symboles perpétuels trouvés: ${perpetualSymbols.length}`);

    return NextResponse.json(perpetualSymbols);

  } catch (error) {
    console.error('Erreur récupération symboles perpétuels:', error);
    
    // Fallback avec des symboles populaires
    const fallbackSymbols = [
      { symbol: 'BTC-USDT', price: 0, change: 0, volume: 0, leverage: '125' },
      { symbol: 'ETH-USDT', price: 0, change: 0, volume: 0, leverage: '100' },
      { symbol: 'BNB-USDT', price: 0, change: 0, volume: 0, leverage: '75' }
    ];

    return NextResponse.json(fallbackSymbols);
  }
}
