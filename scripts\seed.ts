
import { prisma } from '../lib/db';
import { TradingMode, TradeSide, TradeType, TradeStatus } from '@prisma/client';

async function main() {
  console.log('🌱 Début du seeding de la base de données...');

  // Créer un utilisateur de test
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Utilisateur Demo',
      mode: TradingMode.DEMO,
      demoBalance: {
        create: {
          balance: 1000,
          currency: 'USDT'
        }
      }
    },
    include: {
      demoBalance: true
    }
  });

  console.log('✅ Utilisateur de test créé:', testUser.email);

  // Créer quelques trades de test
  const testTrades = [
    {
      userId: testUser.id,
      symbol: 'BTC-USDT',
      side: TradeSide.BUY,
      type: TradeType.MARKET,
      quantity: 0.001,
      executedPrice: 45000,
      status: TradeStatus.FILLED,
      mode: TradingMode.DEMO,
    },
    {
      userId: testUser.id,
      symbol: 'ETH-USDT',
      side: TradeSide.BUY,
      type: TradeType.LIMIT,
      quantity: 0.01,
      price: 2800,
      executedPrice: 2800,
      status: TradeStatus.FILLED,
      mode: TradingMode.DEMO,
    },
    {
      userId: testUser.id,
      symbol: 'BTC-USDT',
      side: TradeSide.SELL,
      type: TradeType.MARKET,
      quantity: 0.0005,
      executedPrice: 45200,
      status: TradeStatus.FILLED,
      mode: TradingMode.DEMO,
      profit: 0.1, // Petit profit
    },
  ];

  for (const trade of testTrades) {
    await prisma.trade.create({
      data: trade
    });
  }

  console.log('✅ Trades de test créés');

  // Créer quelques positions de test
  const testPositions = [
    {
      userId: testUser.id,
      symbol: 'BTC-USDT',
      side: TradeSide.LONG,
      quantity: 0.0005,
      entryPrice: 45100,
      currentPrice: 45300,
      unrealizedPnl: 0.1,
      mode: TradingMode.DEMO,
      isActive: true,
    },
    {
      userId: testUser.id,
      symbol: 'ETH-USDT',
      side: TradeSide.LONG,
      quantity: 0.01,
      entryPrice: 2800,
      currentPrice: 2820,
      unrealizedPnl: 0.2,
      mode: TradingMode.DEMO,
      isActive: true,
    },
  ];

  for (const position of testPositions) {
    await prisma.position.create({
      data: position
    });
  }

  console.log('✅ Positions de test créées');
  console.log('🎉 Seeding terminé avec succès!');

  console.log('\n📋 Résumé:');
  console.log(`- Utilisateur: ${testUser.email}`);
  console.log(`- Solde démo: ${testUser.demoBalance?.balance} USDT`);
  console.log(`- Trades: ${testTrades.length} créés`);
  console.log(`- Positions: ${testPositions.length} créées`);
}

main()
  .catch((e) => {
    console.error('❌ Erreur durant le seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
