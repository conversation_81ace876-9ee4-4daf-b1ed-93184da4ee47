
import { prisma } from './db';
import { TradingMode, TradeSide, TradeType, TradeStatus } from '@prisma/client';

export class DemoTradingService {
  // Obtenir le solde démo d'un utilisateur
  static async getDemoBalance(userId: string) {
    let demoBalance = await prisma.demoBalance.findUnique({
      where: { userId }
    });

    if (!demoBalance) {
      // Créer un solde démo par défaut
      demoBalance = await prisma.demoBalance.create({
        data: {
          userId,
          balance: 1000, // 1000 USDT par défaut
          currency: 'USDT'
        }
      });
    }

    return demoBalance;
  }

  // Simuler l'exécution d'un ordre en mode démo
  static async executeDemoOrder(
    userId: string,
    symbol: string,
    side: TradeSide,
    type: TradeType,
    quantity: number,
    price?: number
  ) {
    const demoBalance = await this.getDemoBalance(userId);
    
    // Pour simplifier, on utilise un prix simulé (en réalité, on utiliserait l'API BingX pour le prix actuel)
    const executedPrice = price || this.getSimulatedPrice(symbol);
    const totalCost = quantity * executedPrice;

    // Vérifier si l'utilisateur a assez de fonds
    if (side === TradeSide.BUY && demoBalance.balance.toNumber() < totalCost) {
      throw new Error('Fonds insuffisants pour cette transaction');
    }

    if (side === TradeSide.SELL) {
      // Pour une vente, on vérifierait normalement si l'utilisateur possède l'actif
      // Pour la démo, on simplifie en permettant la vente
    }

    // Créer l'enregistrement du trade
    const trade = await prisma.trade.create({
      data: {
        userId,
        symbol,
        side,
        type,
        quantity: quantity,
        price: price || null,
        executedPrice: executedPrice,
        status: TradeStatus.FILLED,
        mode: TradingMode.DEMO,
      }
    });

    // Mettre à jour le solde démo
    const newBalance = side === TradeSide.BUY 
      ? demoBalance.balance.toNumber() - totalCost
      : demoBalance.balance.toNumber() + totalCost;

    await prisma.demoBalance.update({
      where: { userId },
      data: { balance: newBalance }
    });

    return trade;
  }

  // Prix simulé pour les tests (en production, utiliser l'API BingX)
  private static getSimulatedPrice(symbol: string): number {
    const prices: Record<string, number> = {
      'BTC-USDT': 45000 + (Math.random() - 0.5) * 1000,
      'ETH-USDT': 2800 + (Math.random() - 0.5) * 100,
      'BNB-USDT': 300 + (Math.random() - 0.5) * 20,
      'ADA-USDT': 0.5 + (Math.random() - 0.5) * 0.1,
      'DOT-USDT': 7 + (Math.random() - 0.5) * 1,
    };

    return prices[symbol] || 100;
  }

  // Obtenir l'historique des trades démo
  static async getDemoTrades(userId: string, limit: number = 50) {
    return prisma.trade.findMany({
      where: {
        userId,
        mode: TradingMode.DEMO
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  // Obtenir les positions ouvertes en mode démo
  static async getDemoPositions(userId: string) {
    return prisma.position.findMany({
      where: {
        userId,
        mode: TradingMode.DEMO,
        isActive: true
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  // Réinitialiser le solde démo
  static async resetDemoBalance(userId: string) {
    await prisma.demoBalance.upsert({
      where: { userId },
      update: { balance: 1000 },
      create: {
        userId,
        balance: 1000,
        currency: 'USDT'
      }
    });

    return { message: 'Solde démo réinitialisé à 1000 USDT' };
  }
}
