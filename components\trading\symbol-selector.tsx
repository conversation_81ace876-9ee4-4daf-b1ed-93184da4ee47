
'use client';

import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, TrendingUp, Volume2 } from 'lucide-react';
import { toast } from 'sonner';

interface Symbol {
  symbol: string;
  baseAsset: string;
  quoteAsset: string;
  status: string;
  volume: string;
  close: string;
  change: string;
}

interface SymbolSelectorProps {
  selectedSymbol: string;
  onSymbolChange: (symbol: string) => void;
}

export function SymbolSelector({ selectedSymbol, onSymbolChange }: SymbolSelectorProps) {
  const [symbols, setSymbols] = useState<Symbol[]>([]);
  const [filteredSymbols, setFilteredSymbols] = useState<Symbol[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [tickers, setTickers] = useState<Record<string, any>>({});

  const fetchSymbols = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/bingx/symbols');
      if (!response.ok) throw new Error('Erreur récupération symboles');
      
      const data = await response.json();
      setSymbols(data || []);
      setFilteredSymbols(data || []);
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement des paires');
    } finally {
      setLoading(false);
    }
  };

  const fetchTickers = async () => {
    try {
      const response = await fetch('/api/bingx/ticker');
      if (!response.ok) return;
      
      const data = await response.json();
      const tickerMap: Record<string, any> = {};
      
      if (Array.isArray(data)) {
        data.forEach(ticker => {
          if (ticker?.symbol) {
            tickerMap[ticker.symbol] = ticker;
          }
        });
      }
      
      setTickers(tickerMap);
    } catch (error) {
      console.error('Erreur récupération tickers:', error);
    }
  };

  useEffect(() => {
    fetchSymbols();
    fetchTickers();
    
    // Auto-refresh tickers toutes les 30 secondes
    const intervalId: NodeJS.Timeout = setInterval(() => {
      fetchTickers();
    }, 30000);
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    const filtered = symbols?.filter?.(symbol =>
      symbol?.symbol?.toLowerCase?.()?.includes?.(searchTerm?.toLowerCase?.() || '') ||
      symbol?.baseAsset?.toLowerCase?.()?.includes?.(searchTerm?.toLowerCase?.() || '')
    ) || [];
    
    setFilteredSymbols(filtered);
  }, [searchTerm, symbols]);

  const formatPrice = (price: string | number) => {
    const num = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(num)) return '0.00';
    if (num > 1) return num.toFixed(2);
    if (num > 0.1) return num.toFixed(4);
    return num.toFixed(6);
  };

  const formatVolume = (volume: string | number) => {
    const num = typeof volume === 'string' ? parseFloat(volume) : volume;
    if (isNaN(num)) return '0';
    if (num >= 1e9) return `${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(1)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const popularPairs = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'SOL-USDT', 'DOGE-USDT'];

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          Sélection de paire
        </CardTitle>
        <CardDescription className="text-slate-400">
          Choisissez une paire de trading parmi {symbols?.length || 0} disponibles
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sélecteur rapide */}
        <div>
          <div className="text-sm text-slate-400 mb-2">Paires populaires:</div>
          <div className="flex flex-wrap gap-2">
            {popularPairs.map(pair => {
              const ticker = tickers?.[pair];
              const change = ticker?.priceChangePercent ? parseFloat(ticker.priceChangePercent) : 0;
              
              return (
                <Button
                  key={pair}
                  variant={selectedSymbol === pair ? "default" : "outline"}
                  size="sm"
                  onClick={() => onSymbolChange(pair)}
                  className={`${
                    selectedSymbol === pair 
                      ? "bg-blue-600 text-white" 
                      : "bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
                  } flex items-center space-x-1`}
                >
                  <span>{pair.split('-')[0]}</span>
                  {change !== 0 && (
                    <Badge 
                      variant="secondary"
                      className={`text-xs px-1 ${
                        change > 0 ? 'bg-green-600/20 text-green-400' : 'bg-red-600/20 text-red-400'
                      }`}
                    >
                      {change > 0 ? '+' : ''}{!isNaN(change) ? change.toFixed(1) : '0.0'}%
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </div>

        {/* Recherche */}
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Rechercher une paire (ex: BTC, ETH...)"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
          />
        </div>

        {/* Liste des symboles filtrés */}
        {searchTerm && (
          <div className="max-h-60 overflow-y-auto space-y-1">
            {loading ? (
              <div className="text-center text-slate-400 py-4">Chargement...</div>
            ) : filteredSymbols?.length > 0 ? (
              filteredSymbols.slice(0, 20).map((symbol, index) => {
                const ticker = tickers?.[symbol?.symbol || ''];
                const price = ticker?.lastPrice || symbol?.close || '0';
                const change = ticker?.priceChangePercent ? parseFloat(ticker.priceChangePercent) : 
                  symbol?.change ? parseFloat(symbol.change) : 0;
                const volume = ticker?.volume || symbol?.volume || '0';
                
                return (
                  <div
                    key={symbol?.symbol || `symbol-${index}`}
                    onClick={() => onSymbolChange(symbol?.symbol || '')}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedSymbol === symbol?.symbol
                        ? 'bg-blue-600/20 border border-blue-600'
                        : 'bg-slate-800/50 hover:bg-slate-800'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-xs font-bold text-white">
                            {symbol?.baseAsset?.slice?.(0, 2) || 'XX'}
                          </span>
                        </div>
                        <div>
                          <div className="text-white font-medium">{symbol?.symbol || 'Unknown'}</div>
                          <div className="text-xs text-slate-400 flex items-center">
                            <Volume2 className="w-3 h-3 mr-1" />
                            Vol: {formatVolume(volume)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">
                          ${formatPrice(price)}
                        </div>
                        <div className={`text-xs ${
                          change > 0 ? 'text-green-400' : change < 0 ? 'text-red-400' : 'text-slate-400'
                        }`}>
                          {change > 0 ? '+' : ''}{!isNaN(change) ? change.toFixed(2) : '0.00'}%
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center text-slate-400 py-4">
                Aucune paire trouvée pour "{searchTerm}"
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
