
import { NextRequest, NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';
import { DemoTradingService } from '@/lib/demo-trading';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const mode = searchParams.get('mode') as 'DEMO' | 'LIVE';
    const tradingType = searchParams.get('tradingType') || 'SPOT';

    if (!userId || !mode) {
      return NextResponse.json(
        { error: 'UserId et mode requis' },
        { status: 400 }
      );
    }

    if (mode === 'DEMO') {
      const demoBalance = await DemoTradingService.getDemoBalance(userId);
      return NextResponse.json([{
        asset: 'USDT',
        free: demoBalance.balance.toString(),
        locked: '0'
      }]);
    } else {
      // Mode LIVE - utiliser l'API BingX avec le bon type de trading
      let balance;
      
      try {
        balance = await bingxApi.getBalanceByType(tradingType);
      } catch (apiError) {
        console.error('Erreur API BingX:', apiError);
        // Fallback vers le spot trading si l'API spécifique échoue
        if (tradingType !== 'SPOT') {
          balance = await bingxApi.getBalance();
        } else {
          throw apiError;
        }
      }
      
      return NextResponse.json(balance);
    }

  } catch (error) {
    console.error('Erreur récupération solde:', error);
    return NextResponse.json(
      { error: 'Erreur récupération du solde' },
      { status: 500 }
    );
  }
}
