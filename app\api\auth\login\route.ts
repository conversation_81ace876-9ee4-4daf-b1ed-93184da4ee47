

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { TradingMode } from '@prisma/client';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, mode } = body;

    console.log('API Login appelée:', { email, mode });

    if (!email || !mode) {
      console.error('Email ou mode manquant');
      return NextResponse.json(
        { error: 'Email et mode requis' },
        { status: 400 }
      );
    }

    // Trouver ou créer l'utilisateur
    let user = await prisma.user.findUnique({
      where: { email },
      include: { demoBalance: true }
    });

    console.log('Utilisateur trouvé:', user ? 'Oui' : 'Non');

    if (!user) {
      console.log('Création nouvel utilisateur');
      user = await prisma.user.create({
        data: {
          email,
          name: email.split('@')[0],
          mode: mode as TradingMode,
          demoBalance: {
            create: {
              balance: 1000,
              currency: 'USDT'
            }
          }
        },
        include: { demoBalance: true }
      });
      console.log('Utilisateur créé:', user.id);
    } else {
      // Mettre à jour le mode si nécessaire
      if (user.mode !== mode) {
        console.log('Mise à jour mode:', mode);
        user = await prisma.user.update({
          where: { id: user.id },
          data: { mode: mode as TradingMode },
          include: { demoBalance: true }
        });
      }
    }

    const result = {
      id: user.id,
      email: user.email,
      name: user.name,
      mode: user.mode,
      tradingType: user.tradingType
    };

    console.log('Login réussi:', result);

    return NextResponse.json(result);

  } catch (error) {
    console.error('Erreur login API:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
