
'use client';

import { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';
import { toast } from 'sonner';

interface PriceData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface TradingChartProps {
  symbol: string;
}

export function TradingChart({ symbol }: TradingChartProps) {
  const [data, setData] = useState<PriceData[]>([]);
  const [timeInterval, setTimeInterval] = useState('1h');
  const [loading, setLoading] = useState(false);

  const fetchChartData = async () => {
    if (!symbol) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/bingx/klines?symbol=${symbol}&interval=${timeInterval}&limit=100`);
      if (!response.ok) throw new Error('Erreur récupération données');
      
      const chartData = await response.json();
      
      // Formater les données pour le graphique
      const formattedData = chartData?.map?.((item: any) => ({
        ...item,
        time: new Date(item?.time || 0).getTime(),
        formattedTime: new Date(item?.time || 0).toLocaleTimeString()
      })) || [];
      
      setData(formattedData);
    } catch (error) {
      console.error('Erreur:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChartData();
    
    // Auto-refresh toutes les 30 secondes
    const intervalId: NodeJS.Timeout = setInterval(() => {
      fetchChartData();
    }, 30000);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [symbol, timeInterval]);

  const currentPrice = data?.[data.length - 1]?.close || 0;
  const previousPrice = data?.[data.length - 2]?.close || 0;
  const priceChange = currentPrice - previousPrice;
  const priceChangePercent = previousPrice > 0 ? (priceChange / previousPrice) * 100 : 0;

  const formatPrice = (price: number) => {
    // Vérification et conversion robuste en nombre
    const numPrice = typeof price === 'number' && !isNaN(price) ? price : null;
    if (!numPrice || numPrice === 0) return '--';
    
    if (numPrice > 1) return numPrice.toFixed(2);
    if (numPrice > 0.1) return numPrice.toFixed(4);
    return numPrice.toFixed(6);
  };

  return (
    <Card className="bg-slate-900/50 border-slate-700">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-white">
            {symbol} {currentPrice > 0 && `- $${formatPrice(currentPrice)}`}
          </CardTitle>
          <CardDescription className="flex items-center space-x-2">
            {priceChange !== 0 && (
              <span className={`flex items-center ${priceChange > 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceChange > 0 ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
                {priceChange > 0 ? '+' : ''}{formatPrice(priceChange)} 
                ({priceChangePercent > 0 ? '+' : ''}{!isNaN(priceChangePercent) ? priceChangePercent.toFixed(2) : '0.00'}%)
              </span>
            )}
          </CardDescription>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeInterval} onValueChange={setTimeInterval}>
            <SelectTrigger className="w-20 bg-slate-800 border-slate-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-600">
              <SelectItem value="1m">1m</SelectItem>
              <SelectItem value="5m">5m</SelectItem>
              <SelectItem value="15m">15m</SelectItem>
              <SelectItem value="1h">1h</SelectItem>
              <SelectItem value="4h">4h</SelectItem>
              <SelectItem value="1d">1d</SelectItem>
            </SelectContent>
          </Select>
          <Button
            size="sm"
            variant="outline"
            onClick={fetchChartData}
            disabled={loading}
            className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
          >
            <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="formattedTime"
                tick={{ fontSize: 10, fill: '#9CA3AF' }}
                tickLine={false}
                interval="preserveStartEnd"
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis 
                tick={{ fontSize: 10, fill: '#9CA3AF' }}
                tickLine={false}
                domain={['dataMin - dataMin * 0.01', 'dataMax + dataMax * 0.01']}
                tickFormatter={formatPrice}
              />
              <Tooltip
                labelStyle={{ color: '#ffffff', fontSize: 11 }}
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  fontSize: 11
                }}
                formatter={(value: any) => [`$${formatPrice(Number(value))}`, 'Prix']}
              />
              <Line 
                type="monotone" 
                dataKey="close" 
                stroke="#60B5FF" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, stroke: '#60B5FF', strokeWidth: 2 }}
              />
              {currentPrice > 0 && (
                <ReferenceLine 
                  y={currentPrice} 
                  stroke="#60B5FF" 
                  strokeDasharray="5 5" 
                  strokeOpacity={0.7}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
