
'use client';

import { useState, useEffect } from 'react';
import { LoginForm } from '@/components/auth/login-form';
import { Header } from '@/components/layout/header';
import { SymbolSelector } from '@/components/trading/symbol-selector';
import { TradingChart } from '@/components/trading/trading-chart';
import { OrderForm } from '@/components/trading/order-form';
import { BalanceDisplay } from '@/components/trading/balance-display';
import { TradesHistory } from '@/components/trading/trades-history';
import { PositionsList } from '@/components/trading/positions-list';
import { TradingModeSelector } from '@/components/trading/trading-mode-selector';
import { useAuth, useAuthInit } from '@/lib/auth';
import { toast, Toaster } from 'sonner';

export default function TradingPlatform() {
  const user = useAuthInit();
  const [selectedSymbol, setSelectedSymbol] = useState('BTC-USDT');
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [refreshKey, setRefreshKey] = useState(0);

  // Fonction pour rafraîchir les données après un ordre
  const handleOrderPlaced = () => {
    setRefreshKey(prev => prev + 1);
    toast.success('Ordre placé avec succès');
  };

  // Récupérer le prix actuel du symbole sélectionné
  useEffect(() => {
    const fetchCurrentPrice = async () => {
      if (!selectedSymbol) return;
      
      try {
        const response = await fetch(`/api/bingx/ticker?symbol=${selectedSymbol}`);
        if (response.ok) {
          const data = await response.json();
          const price = Array.isArray(data) ? data[0]?.lastPrice : data?.lastPrice;
          if (price) {
            setCurrentPrice(parseFloat(price));
          }
        }
      } catch (error) {
        console.error('Erreur prix actuel:', error);
      }
    };

    fetchCurrentPrice();
    
    // Mettre à jour le prix toutes les 10 secondes
    const intervalId = setInterval(fetchCurrentPrice, 10000);
    return () => clearInterval(intervalId);
  }, [selectedSymbol]);

  if (!user) {
    return (
      <>
        <LoginForm onSuccess={() => window.location.reload()} />
        <Toaster position="top-right" theme="dark" />
      </>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
      <Header />
      
      <main className="container max-w-7xl mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Colonne gauche - Sélection et graphique */}
          <div className="lg:col-span-2 space-y-6">
            <SymbolSelector 
              selectedSymbol={selectedSymbol}
              onSymbolChange={setSelectedSymbol}
            />

            <TradingModeSelector />
            
            <TradingChart symbol={selectedSymbol} />
            
            {/* Historique des trades */}
            <div key={`history-${refreshKey}`}>
              <TradesHistory />
            </div>
          </div>

          {/* Colonne droite - Trading et portfolio */}
          <div className="space-y-6">
            {/* Solde et portfolio */}
            <div key={`balance-${refreshKey}`}>
              <BalanceDisplay />
            </div>

            {/* Formulaire d'ordre */}
            <OrderForm 
              symbol={selectedSymbol}
              currentPrice={currentPrice}
              onOrderPlaced={handleOrderPlaced}
            />

            {/* Positions ouvertes */}
            <div key={`positions-${refreshKey}`}>
              <PositionsList />
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-12 py-8 border-t border-slate-700">
          <div className="text-center text-slate-400 space-y-2">
            <p className="text-sm">
              <strong>⚠️ Avertissement:</strong> Le trading de crypto-monnaies est risqué. 
              Ne tradez que ce que vous pouvez vous permettre de perdre.
            </p>
            <p className="text-xs">
              🟢 Mode Démo: Solde virtuel pour l'entraînement | 🔴 Mode Réel: Trading avec API BingX
            </p>
          </div>
        </footer>
      </main>

      <Toaster position="top-right" theme="dark" />
    </div>
  );
}
