
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useAuth } from '@/lib/auth';
import { TradeSide, TradeType } from '@prisma/client';
import { ShoppingCart, TrendingDown, AlertTriangle, DollarSign } from 'lucide-react';
import { toast } from 'sonner';

interface OrderFormProps {
  symbol: string;
  currentPrice?: number;
  onOrderPlaced?: () => void;
}

export function OrderForm({ symbol, currentPrice, onOrderPlaced }: OrderFormProps) {
  const { user } = useAuth();
  const [side, setSide] = useState<TradeSide>(TradeSide.BUY);
  const [type, setType] = useState<TradeType>(TradeType.MARKET);
  const [quantity, setQuantity] = useState('');
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error('Vous devez être connecté');
      return;
    }

    if (!quantity || parseFloat(quantity) <= 0) {
      toast.error('Veuillez saisir une quantité valide');
      return;
    }

    if (type === TradeType.LIMIT && (!price || parseFloat(price) <= 0)) {
      toast.error('Veuillez saisir un prix valide pour un ordre limite');
      return;
    }

    // Confirmation pour les ordres en mode réel
    if (user.mode === 'LIVE') {
      setShowConfirmation(true);
    } else {
      executeOrder();
    }
  };

  const executeOrder = async () => {
    if (!user) return;
    
    setLoading(true);
    setShowConfirmation(false);

    try {
      const orderData = {
        userId: user.id,
        symbol,
        side,
        type,
        quantity,
        price: type === TradeType.LIMIT ? price : undefined,
        mode: user.mode
      };

      const response = await fetch('/api/trading/order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Erreur lors de la création de l\'ordre');
      }

      const result = await response.json();
      toast.success(result.message || 'Ordre créé avec succès');
      
      // Réinitialiser le formulaire
      setQuantity('');
      setPrice('');
      
      // Callback pour rafraîchir les données
      onOrderPlaced?.();
      
    } catch (error) {
      console.error('Erreur ordre:', error);
      toast.error(error instanceof Error ? error.message : 'Erreur lors de la création de l\'ordre');
    } finally {
      setLoading(false);
    }
  };

  const estimatedCost = (() => {
    if (!currentPrice || !quantity) return '0';
    
    const qty = parseFloat(quantity || '0');
    const priceToUse = type === TradeType.LIMIT ? parseFloat(price || '0') : currentPrice;
    
    if (isNaN(qty) || isNaN(priceToUse) || qty <= 0 || priceToUse <= 0) return '0';
    
    const cost = qty * priceToUse;
    return isNaN(cost) ? '0' : cost.toFixed(2);
  })();

  const formatPrice = (price: number) => {
    // Vérification et conversion robuste en nombre
    const numPrice = typeof price === 'number' && !isNaN(price) ? price : null;
    if (!numPrice || numPrice === 0) return '--';
    
    if (numPrice > 1) return numPrice.toFixed(2);
    if (numPrice > 0.1) return numPrice.toFixed(4);
    return numPrice.toFixed(6);
  };

  return (
    <>
      <Card className="bg-slate-900/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <ShoppingCart className="w-5 h-5 mr-2" />
            Passer un ordre
          </CardTitle>
          <CardDescription className="text-slate-400">
            {user?.mode === 'DEMO' ? '🟢 Mode Démo' : '🔴 Mode Réel'} - {symbol}
            {currentPrice && (
              <span className="ml-2">Prix actuel: ${formatPrice(currentPrice)}</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Tabs value={side} onValueChange={(value) => setSide(value as TradeSide)}>
              <TabsList className="grid w-full grid-cols-2 bg-slate-800">
                <TabsTrigger value={TradeSide.BUY} className="text-green-400 data-[state=active]:bg-green-600/20">
                  <TrendingDown className="w-4 h-4 mr-1 rotate-180" />
                  Acheter
                </TabsTrigger>
                <TabsTrigger value={TradeSide.SELL} className="text-red-400 data-[state=active]:bg-red-600/20">
                  <TrendingDown className="w-4 h-4 mr-1" />
                  Vendre
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="space-y-2">
              <Label className="text-slate-200">Type d'ordre</Label>
              <Select value={type} onValueChange={(value) => setType(value as TradeType)}>
                <SelectTrigger className="bg-slate-800 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-600">
                  <SelectItem value={TradeType.MARKET}>Marché</SelectItem>
                  <SelectItem value={TradeType.LIMIT}>Limite</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity" className="text-slate-200">Quantité</Label>
              <Input
                id="quantity"
                type="number"
                step="any"
                placeholder="0.00"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
                required
              />
            </div>

            {type === TradeType.LIMIT && (
              <div className="space-y-2">
                <Label htmlFor="price" className="text-slate-200">Prix (USDT)</Label>
                <Input
                  id="price"
                  type="number"
                  step="any"
                  placeholder="0.00"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
                  required
                />
              </div>
            )}

            {estimatedCost && parseFloat(estimatedCost) > 0 && (
              <div className="bg-slate-800/50 p-3 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-400">Coût estimé:</span>
                  <span className="text-white font-medium">${estimatedCost} USDT</span>
                </div>
              </div>
            )}

            <Button 
              type="submit" 
              className={`w-full ${
                side === TradeSide.BUY 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-red-600 hover:bg-red-700'
              } text-white`}
              disabled={loading || !symbol}
            >
              <DollarSign className="w-4 h-4 mr-2" />
              {loading ? 'Traitement...' : 
                `${side === TradeSide.BUY ? 'Acheter' : 'Vendre'} ${symbol.split('-')[0]}`
              }
            </Button>
          </form>
        </CardContent>
      </Card>

      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent className="bg-slate-900 border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-red-400" />
              Confirmer l'ordre réel
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400">
              Vous êtes sur le point de passer un ordre <strong>réel</strong> sur BingX. 
              Cet ordre utilisera de vrais fonds et ne peut pas être annulé une fois exécuté.
              <br /><br />
              <strong>Détails:</strong>
              <br />• {side === TradeSide.BUY ? 'Acheter' : 'Vendre'} {quantity} {symbol.split('-')[0]}
              <br />• Type: {type === TradeType.MARKET ? 'Marché' : 'Limite'}
              {type === TradeType.LIMIT && <><br />• Prix: ${price} USDT</>}
              <br />• Coût estimé: ${estimatedCost} USDT
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-slate-700 text-white hover:bg-slate-600">
              Annuler
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={executeOrder}
              className={`${
                side === TradeSide.BUY 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-red-600 hover:bg-red-700'
              } text-white`}
            >
              Confirmer l'ordre
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
