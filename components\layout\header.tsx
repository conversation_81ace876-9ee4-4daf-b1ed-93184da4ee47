
'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { TradingMode } from '@prisma/client';
import { LogOut, Settings, ToggleLeft, ToggleRight, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';

export function Header() {
  const { user, logout, switchMode } = useAuth();

  const handleModeSwitch = async () => {
    if (!user) return;
    
    const newMode = user.mode === TradingMode.DEMO ? TradingMode.LIVE : TradingMode.DEMO;
    
    try {
      await switchMode(newMode);
      toast.success(`Basculé en mode ${newMode === TradingMode.DEMO ? 'Démo' : 'Réel'}`);
    } catch (error) {
      toast.error('Erreur lors du changement de mode');
    }
  };

  const handleLogout = () => {
    logout();
    toast.success('Déconnexion réussie');
  };

  if (!user) return null;

  return (
    <header className="sticky top-0 z-50 w-full border-b border-slate-700 bg-slate-950/80 backdrop-blur supports-[backdrop-filter]:bg-slate-950/60">
      <div className="container max-w-7xl mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo et titre */}
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-full bg-blue-600/20">
            <TrendingUp className="h-6 w-6 text-blue-400" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">BingX Trading</h1>
            <p className="text-xs text-slate-400">Plateforme de trading simple</p>
          </div>
        </div>

        {/* Indicateurs de mode et utilisateur */}
        <div className="flex items-center space-x-4">
          {/* Mode Trading */}
          <div className="flex items-center space-x-2">
            <Badge 
              className={`${
                user.mode === TradingMode.DEMO 
                  ? 'bg-green-600/20 text-green-400 border-green-600' 
                  : 'bg-red-600/20 text-red-400 border-red-600'
              }`}
            >
              {user.mode === TradingMode.DEMO ? '🟢 DEMO' : '🔴 LIVE'}
            </Badge>
            <Button
              size="sm"
              variant="outline"
              onClick={handleModeSwitch}
              className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
            >
              {user.mode === TradingMode.DEMO ? (
                <ToggleLeft className="w-4 h-4 mr-1" />
              ) : (
                <ToggleRight className="w-4 h-4 mr-1" />
              )}
              Basculer
            </Button>
          </div>

          {/* Informations utilisateur */}
          <div className="flex items-center space-x-2 text-white">
            <div className="text-right">
              <div className="text-sm font-medium">{user.name || 'Utilisateur'}</div>
              <div className="text-xs text-slate-400">{user.email}</div>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={handleLogout}
              className="bg-slate-800 border-slate-600 text-white hover:bg-slate-700"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
