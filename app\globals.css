
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Supprimer les erreurs d'hydratation dans la console */
  #__next {
    @apply isolate;
  }
}

@layer components {
  .trading-grid {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
  }
  
  .trading-card {
    @apply bg-slate-900/50 border-slate-700 backdrop-blur-sm;
  }
  
  .price-positive {
    @apply text-green-400;
  }
  
  .price-negative {
    @apply text-red-400;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950;
  }
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Animation pour les éléments qui apparaissent */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeInUp 0.5s ease-out;
}

/* Animation pour les chiffres qui changent */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.price-update {
  animation: pulse 0.3s ease-in-out;
}

/* Éviter les problèmes d'hydratation avec les transitions */
.no-hydration-errors * {
  transition: none !important;
}

html.no-transitions *,
html.no-transitions *:before,
html.no-transitions *:after {
  transition: none !important;
  transition-delay: none !important;
  animation-duration: 0s !important;
  animation-delay: 0s !important;
}
