
import { NextResponse } from 'next/server';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET() {
  try {
    const response = await bingxApi.getAllSymbols();
    
    // Filtrer et formater les symboles pour ne garder que ceux avec USDT
    const symbols = response?.data?.symbols || [];
    const usdtSymbols = symbols.filter((symbol: any) => 
      symbol?.status === 1 && symbol?.symbol?.endsWith('USDT')
    ) || [];

    // Trier par volume décroissant
    const sortedSymbols = usdtSymbols.sort((a: any, b: any) => 
      parseFloat(b?.volume || '0') - parseFloat(a?.volume || '0')
    );

    return NextResponse.json(sortedSymbols.slice(0, 50)); // Top 50 paires

  } catch (error) {
    console.error('Erreur récupération symboles:', error);
    return NextResponse.json(
      { error: 'Erreur récupération des symboles' },
      { status: 500 }
    );
  }
}
