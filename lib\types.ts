
import { TradingMode, TradeSide, TradeType, TradeStatus } from '@prisma/client';

// Types de base pour l'API BingX
export interface BingXSymbol {
  symbol: string;
  status: string;
  baseAsset: string;
  quoteAsset: string;
  volume: string;
  count: string;
  close: string;
  change: string;
  amount: string;
}

export interface BingXTicker {
  symbol: string;
  priceChange: string;
  priceChangePercent: string;
  weightedAvgPrice: string;
  prevClosePrice: string;
  lastPrice: string;
  bidPrice: string;
  askPrice: string;
  openPrice: string;
  highPrice: string;
  lowPrice: string;
  volume: string;
  quoteVolume: string;
  openTime: number;
  closeTime: number;
  firstId: number;
  lastId: number;
  count: number;
}

export interface BingXBalance {
  asset: string;
  free: string;
  locked: string;
}

export interface BingXOrder {
  symbol: string;
  orderId: number;
  orderListId: number;
  clientOrderId: string;
  price: string;
  origQty: string;
  executedQty: string;
  cummulativeQuoteQty: string;
  status: string;
  timeInForce: string;
  type: string;
  side: string;
  stopPrice: string;
  icebergQty: string;
  time: number;
  updateTime: number;
  isWorking: boolean;
}

// Types pour notre application
export interface User {
  id: string;
  email: string;
  name?: string;
  mode: TradingMode;
  createdAt: Date;
  updatedAt: Date;
}

export interface Trade {
  id: string;
  userId: string;
  symbol: string;
  side: TradeSide;
  type: TradeType;
  quantity: number;
  price?: number;
  executedPrice?: number;
  status: TradeStatus;
  mode: TradingMode;
  profit?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Position {
  id: string;
  userId: string;
  symbol: string;
  side: TradeSide;
  quantity: number;
  entryPrice: number;
  currentPrice?: number;
  unrealizedPnl?: number;
  mode: TradingMode;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DemoBalance {
  id: string;
  userId: string;
  balance: number;
  currency: string;
  createdAt: Date;
  updatedAt: Date;
}

// Types pour les formulaires
export interface OrderFormData {
  symbol: string;
  side: TradeSide;
  type: TradeType;
  quantity: string;
  price?: string;
}

// Types pour les composants UI
export interface PriceData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MarketData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
}

// Export des types Prisma
export { TradingMode, TradeSide, TradeType, TradeStatus };
