import { ComponentType } from 'react';
import { ContainerProps, IndicatorsContainerProps, ValueContainerProps } from './containers';
import { ClearIndicatorProps, CrossIconProps, DownChevronProps, DropdownIndicatorProps, IndicatorSeparatorProps, LoadingIndicatorProps } from './indicators';
import { ControlProps } from './Control';
import { GroupHeadingProps, GroupProps } from './Group';
import { InputProps } from './Input';
import { MenuListProps, MenuPortalProps, MenuProps, NoticeProps } from './Menu';
import { MultiValueGenericProps, MultiValueProps, MultiValueRemove, MultiValueRemoveProps } from './MultiValue';
import { OptionProps } from './Option';
import { PlaceholderProps } from './Placeholder';
import { SingleValueProps } from './SingleValue';
import { GroupBase } from '../types';
export interface SelectComponents<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
    ClearIndicator: ComponentType<ClearIndicatorProps<Option, IsMulti, Group>>;
    Control: ComponentType<ControlProps<Option, IsMulti, Group>>;
    DropdownIndicator: ComponentType<DropdownIndicatorProps<Option, IsMulti, Group>> | null;
    DownChevron: ComponentType<DownChevronProps>;
    CrossIcon: ComponentType<CrossIconProps>;
    Group: ComponentType<GroupProps<Option, IsMulti, Group>>;
    GroupHeading: ComponentType<GroupHeadingProps<Option, IsMulti, Group>>;
    IndicatorsContainer: ComponentType<IndicatorsContainerProps<Option, IsMulti, Group>>;
    IndicatorSeparator: ComponentType<IndicatorSeparatorProps<Option, IsMulti, Group>> | null;
    Input: ComponentType<InputProps<Option, IsMulti, Group>>;
    LoadingIndicator: ComponentType<LoadingIndicatorProps<Option, IsMulti, Group>>;
    Menu: ComponentType<MenuProps<Option, IsMulti, Group>>;
    MenuList: ComponentType<MenuListProps<Option, IsMulti, Group>>;
    MenuPortal: ComponentType<MenuPortalProps<Option, IsMulti, Group>>;
    LoadingMessage: ComponentType<NoticeProps<Option, IsMulti, Group>>;
    NoOptionsMessage: ComponentType<NoticeProps<Option, IsMulti, Group>>;
    MultiValue: ComponentType<MultiValueProps<Option, IsMulti, Group>>;
    MultiValueContainer: ComponentType<MultiValueGenericProps<Option, IsMulti, Group>>;
    MultiValueLabel: ComponentType<MultiValueGenericProps<Option, IsMulti, Group>>;
    MultiValueRemove: ComponentType<MultiValueRemoveProps<Option, IsMulti, Group>>;
    Option: ComponentType<OptionProps<Option, IsMulti, Group>>;
    Placeholder: ComponentType<PlaceholderProps<Option, IsMulti, Group>>;
    SelectContainer: ComponentType<ContainerProps<Option, IsMulti, Group>>;
    SingleValue: ComponentType<SingleValueProps<Option, IsMulti, Group>>;
    ValueContainer: ComponentType<ValueContainerProps<Option, IsMulti, Group>>;
}
export declare type SelectComponentsConfig<Option, IsMulti extends boolean, Group extends GroupBase<Option>> = Partial<SelectComponents<Option, IsMulti, Group>>;
export declare const components: {
    ClearIndicator: <Option, IsMulti extends boolean, Group extends GroupBase<Option>>(props: ClearIndicatorProps<Option, IsMulti, Group>) => import("@emotion/react").jsx.JSX.Element;
    Control: <Option_1, IsMulti_1 extends boolean, Group_1 extends GroupBase<Option_1>>(props: ControlProps<Option_1, IsMulti_1, Group_1>) => import("@emotion/react").jsx.JSX.Element;
    DropdownIndicator: <Option_2, IsMulti_2 extends boolean, Group_2 extends GroupBase<Option_2>>(props: DropdownIndicatorProps<Option_2, IsMulti_2, Group_2>) => import("@emotion/react").jsx.JSX.Element;
    DownChevron: (props: DownChevronProps) => import("@emotion/react").jsx.JSX.Element;
    CrossIcon: (props: CrossIconProps) => import("@emotion/react").jsx.JSX.Element;
    Group: <Option_3, IsMulti_3 extends boolean, Group_3 extends GroupBase<Option_3>>(props: GroupProps<Option_3, IsMulti_3, Group_3>) => import("@emotion/react").jsx.JSX.Element;
    GroupHeading: <Option_4, IsMulti_4 extends boolean, Group_4 extends GroupBase<Option_4>>(props: GroupHeadingProps<Option_4, IsMulti_4, Group_4>) => import("@emotion/react").jsx.JSX.Element;
    IndicatorsContainer: <Option_5, IsMulti_5 extends boolean, Group_5 extends GroupBase<Option_5>>(props: IndicatorsContainerProps<Option_5, IsMulti_5, Group_5>) => import("@emotion/react").jsx.JSX.Element;
    IndicatorSeparator: <Option_6, IsMulti_6 extends boolean, Group_6 extends GroupBase<Option_6>>(props: IndicatorSeparatorProps<Option_6, IsMulti_6, Group_6>) => import("@emotion/react").jsx.JSX.Element;
    Input: <Option_7, IsMulti_7 extends boolean, Group_7 extends GroupBase<Option_7>>(props: InputProps<Option_7, IsMulti_7, Group_7>) => import("@emotion/react").jsx.JSX.Element;
    LoadingIndicator: <Option_8, IsMulti_8 extends boolean, Group_8 extends GroupBase<Option_8>>({ innerProps, isRtl, size, ...restProps }: LoadingIndicatorProps<Option_8, IsMulti_8, Group_8>) => import("@emotion/react").jsx.JSX.Element;
    Menu: <Option_9, IsMulti_9 extends boolean, Group_9 extends GroupBase<Option_9>>(props: MenuProps<Option_9, IsMulti_9, Group_9>) => import("@emotion/react").jsx.JSX.Element;
    MenuList: <Option_10, IsMulti_10 extends boolean, Group_10 extends GroupBase<Option_10>>(props: MenuListProps<Option_10, IsMulti_10, Group_10>) => import("@emotion/react").jsx.JSX.Element;
    MenuPortal: <Option_11, IsMulti_11 extends boolean, Group_11 extends GroupBase<Option_11>>(props: MenuPortalProps<Option_11, IsMulti_11, Group_11>) => import("@emotion/react").jsx.JSX.Element | null;
    LoadingMessage: <Option_12, IsMulti_12 extends boolean, Group_12 extends GroupBase<Option_12>>({ children, innerProps, ...restProps }: NoticeProps<Option_12, IsMulti_12, Group_12>) => import("@emotion/react").jsx.JSX.Element;
    NoOptionsMessage: <Option_13, IsMulti_13 extends boolean, Group_13 extends GroupBase<Option_13>>({ children, innerProps, ...restProps }: NoticeProps<Option_13, IsMulti_13, Group_13>) => import("@emotion/react").jsx.JSX.Element;
    MultiValue: <Option_14, IsMulti_14 extends boolean, Group_14 extends GroupBase<Option_14>>(props: MultiValueProps<Option_14, IsMulti_14, Group_14>) => import("@emotion/react").jsx.JSX.Element;
    MultiValueContainer: <Option_15, IsMulti_15 extends boolean, Group_15 extends GroupBase<Option_15>>({ children, innerProps, }: MultiValueGenericProps<Option_15, IsMulti_15, Group_15>) => import("@emotion/react").jsx.JSX.Element;
    MultiValueLabel: <Option_15, IsMulti_15 extends boolean, Group_15 extends GroupBase<Option_15>>({ children, innerProps, }: MultiValueGenericProps<Option_15, IsMulti_15, Group_15>) => import("@emotion/react").jsx.JSX.Element;
    MultiValueRemove: typeof MultiValueRemove;
    Option: <Option_16, IsMulti_16 extends boolean, Group_16 extends GroupBase<Option_16>>(props: OptionProps<Option_16, IsMulti_16, Group_16>) => import("@emotion/react").jsx.JSX.Element;
    Placeholder: <Option_17, IsMulti_17 extends boolean, Group_17 extends GroupBase<Option_17>>(props: PlaceholderProps<Option_17, IsMulti_17, Group_17>) => import("@emotion/react").jsx.JSX.Element;
    SelectContainer: <Option_18, IsMulti_18 extends boolean, Group_18 extends GroupBase<Option_18>>(props: ContainerProps<Option_18, IsMulti_18, Group_18>) => import("@emotion/react").jsx.JSX.Element;
    SingleValue: <Option_19, IsMulti_19 extends boolean, Group_19 extends GroupBase<Option_19>>(props: SingleValueProps<Option_19, IsMulti_19, Group_19>) => import("@emotion/react").jsx.JSX.Element;
    ValueContainer: <Option_20, IsMulti_20 extends boolean, Group_20 extends GroupBase<Option_20>>(props: ValueContainerProps<Option_20, IsMulti_20, Group_20>) => import("@emotion/react").jsx.JSX.Element;
};
export declare type SelectComponentsGeneric = typeof components;
interface Props<Option, IsMulti extends boolean, Group extends GroupBase<Option>> {
    components: SelectComponentsConfig<Option, IsMulti, Group>;
}
export declare const defaultComponents: <Option, IsMulti extends boolean, Group extends GroupBase<Option>>(props: Props<Option, IsMulti, Group>) => SelectComponentsGeneric;
export {};
