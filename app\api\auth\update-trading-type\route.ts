

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { TradingType } from '@prisma/client';

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, tradingType } = body;

    console.log('Mise à jour type de trading:', { userId, tradingType });

    if (!userId || !tradingType) {
      return NextResponse.json(
        { error: 'UserId et tradingType requis' },
        { status: 400 }
      );
    }

    // Vérifier que le trading type est valide
    if (!Object.values(TradingType).includes(tradingType)) {
      return NextResponse.json(
        { error: 'Type de trading invalide' },
        { status: 400 }
      );
    }

    // Mettre à jour l'utilisateur
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { tradingType: tradingType as TradingType }
    });

    console.log('Type de trading mis à jour:', updatedUser.tradingType);

    return NextResponse.json({ 
      success: true, 
      tradingType: updatedUser.tradingType 
    });

  } catch (error) {
    console.error('Erreur mise à jour trading type:', error);
    return NextResponse.json(
      { error: 'Erreur serveur' },
      { status: 500 }
    );
  }
}
