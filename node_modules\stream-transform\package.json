{"version": "3.4.0", "name": "stream-transform", "description": "Object transformations implementing the Node.js `stream.Transform` API", "keywords": ["stream", "transform", "csv", "object", "backend", "frontend"], "author": "<PERSON> <<EMAIL>> (https://www.adaltas.com)", "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.1", "@types/mocha": "^10.0.10", "@types/node": "^22.15.30", "csv-generate": "^4.5.0", "each": "^2.7.2", "mocha": "~11.5.0", "pad": "~3.3.0", "prettier": "^3.5.3", "rollup": "^4.41.1", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "should": "~13.2.3", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./sync": {"import": {"types": "./lib/sync.d.ts", "default": "./lib/sync.js"}, "require": {"types": "./dist/cjs/sync.d.cts", "default": "./dist/cjs/sync.cjs"}}, "./browser/esm": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "./browser/esm/sync": {"types": "./dist/esm/sync.d.ts", "default": "./dist/esm/sync.js"}}, "files": ["dist", "lib"], "homepage": "https://csv.js.org/transform", "license": "MIT", "main": "./dist/cjs/index.cjs", "mocha": {"inline-diffs": true, "loader": "ts-node/esm", "recursive": true, "reporter": "spec", "throw-deprecation": false, "timeout": 40000}, "repository": {"type": "git", "url": "https://github.com/adaltas/node-csv.git", "directory": "packages/stream-transform"}, "scripts": {"build": "npm run build:rollup && npm run build:ts", "build:rollup": "npx rollup -c", "build:ts": "cp lib/index.d.ts dist/cjs/index.d.cts && cp lib/sync.d.ts dist/cjs/sync.d.cts && cp lib/*.ts dist/esm", "postbuild:ts": "find dist/cjs -name '*.d.cts' -exec sh -c \"sed -i \"s/\\.js'/\\.cjs'/g\" {} || sed -i '' \"s/\\.js'/\\.cjs'/g\" {}\" \\;", "lint:check": "eslint && tsc --noEmit true", "lint:fix": "eslint --fix", "lint:ts": "tsc --noEmit true", "preversion": "npm run build && git add dist", "test": "mocha 'test/**/*.{js,ts}'", "test:legacy": "mocha --ignore test/handler.mode.callback.js --ignore test/handler.mode.callback.error.js 'test/**/*.{js,ts}'"}, "type": "module", "types": "dist/esm/index.d.ts", "typesVersions": {"*": {".": ["dist/esm/index.d.ts"], "sync": ["dist/esm/sync.d.ts"], "browser/esm": ["dist/esm/index.d.ts"], "browser/esm/sync": ["dist/esm/sync.d.ts"]}}, "gitHead": "d6b53b3d22404e5cd7dbe8b171c0e6504850d4ee"}