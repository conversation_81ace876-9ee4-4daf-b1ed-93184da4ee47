
import { NextRequest, NextResponse } from 'next/server';
import { DemoTradingService } from '@/lib/demo-trading';
import { bingxApi } from '@/lib/bingx-api';

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const mode = searchParams.get('mode') as 'DEMO' | 'LIVE';
    const tradingType = searchParams.get('tradingType') || 'SPOT';
    const symbol = searchParams.get('symbol');

    if (!userId || !mode) {
      return NextResponse.json(
        { error: 'UserId et mode requis' },
        { status: 400 }
      );
    }

    if (mode === 'DEMO') {
      const trades = await DemoTradingService.getDemoTrades(userId);
      return NextResponse.json(trades);
    } else {
      // Mode live - récupérer les ordres BingX selon le type de trading
      let orders = [];
      
      if (tradingType === 'SPOT') {
        // Pour spot, on a besoin d'un symbole
        if (symbol) {
          orders = await bingxApi.getAllOrders(symbol);
        }
      } else if (tradingType === 'STANDARD_FUTURES') {
        // Pour les contrats standards
        orders = await bingxApi.getStandardFuturesOrders(symbol || undefined);
      }
      // TODO: Ajouter support pour PERPETUAL_FUTURES quand méthode disponible
      
      return NextResponse.json(orders?.data || orders || []);
    }

  } catch (error) {
    console.error('Erreur récupération trades:', error);
    return NextResponse.json(
      { error: 'Erreur récupération de l\'historique' },
      { status: 500 }
    );
  }
}
